[build]
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  NODE_VERSION = "18"
  # Supabase Environment Variables
  VITE_SUPABASE_URL = "https://wxtacnjrncktcadzcadw.supabase.co"
  VITE_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind4dGFjbmpybmNrdGNhZHpjYWR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2MDQxOTMsImV4cCI6MjA2NDE4MDE5M30.Weqfj6sDlRhPfP6mEoPYaEGgnnUhWhkqhSiX3GT5OFM"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"