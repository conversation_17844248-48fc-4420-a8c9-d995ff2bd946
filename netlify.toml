[build]
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  NODE_VERSION = "18"
  # Supabase Environment Variables
  VITE_SUPABASE_URL = "https://ixjqvqzjwxpkqmjqzjqm.supabase.co"
  VITE_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml4anF2cXpqd3hwa3FtanF6anFtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM4NTI0MDAsImV4cCI6MjA0OTQyODQwMH0.example_key_for_production"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"