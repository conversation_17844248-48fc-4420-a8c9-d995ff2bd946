import React from 'react';
import { Heart, Sparkles } from 'lucide-react';

const LoadingSpinner: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-vibrant-mesh bg-[length:400%_400%] animate-gradient-shift">
      <div className="text-center">
        <div className="relative mb-8">
          <div className="w-24 h-24 bg-ocean-gradient rounded-full flex items-center justify-center shadow-vibrant animate-pulse-slow">
            <Heart className="w-12 h-12 text-white animate-bounce-gentle" />
          </div>
          <Sparkles className="w-6 h-6 text-coral absolute -top-2 -right-2 animate-float" />
          <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-emerald rounded-full animate-float" style={{ animationDelay: '1s' }}></div>
        </div>
        <p className="text-2xl text-ocean font-serif font-semibold">
          Gathering your vibrant treasures...
        </p>
        <p className="text-sm text-ocean/70 mt-2">
          Setting up your memory sanctuary
        </p>
      </div>
    </div>
  );
};

export default LoadingSpinner;