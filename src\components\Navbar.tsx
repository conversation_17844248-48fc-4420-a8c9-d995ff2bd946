import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Heart, User, Upload, Moon, Sun, LogOut, Sparkles } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';

const Navbar: React.FC = () => {
  const { user, logout } = useAuth();
  const { isDark, toggleTheme } = useTheme();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  return (
    <nav className="bg-white/10 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          <Link to={user ? "/dashboard" : "/"} className="flex items-center space-x-4 group">
            <div className="relative">
              <div className="w-12 h-12 bg-coral rounded-2xl flex items-center justify-center shadow-coral transform group-hover:scale-110 transition-all duration-300">
                <Heart className="w-7 h-7 text-white" />
              </div>
              <Sparkles className="w-4 h-4 text-emerald absolute -top-1 -right-1 animate-float" />
            </div>
            <span className="text-3xl font-serif font-bold bg-ocean-gradient bg-clip-text text-transparent">
              Nostoria
            </span>
          </Link>

          <div className="flex items-center space-x-4">
            <button
              onClick={toggleTheme}
              className="p-3 rounded-2xl bg-sky/20 hover:bg-sky/30 transition-all duration-300 hover:scale-110 backdrop-blur-sm"
            >
              {isDark ? (
                <Sun className="w-6 h-6 text-coral" />
              ) : (
                <Moon className="w-6 h-6 text-ocean" />
              )}
            </button>

            {user ? (
              <>
                <Link
                  to="/upload"
                  className="flex items-center space-x-3 bg-ocean-gradient hover:shadow-vibrant text-white px-6 py-3 rounded-2xl transition-all duration-300 transform hover:scale-105 font-semibold"
                >
                  <Upload className="w-5 h-5" />
                  <span>Add Memory</span>
                </Link>
                <Link
                  to="/profile"
                  className="p-3 rounded-2xl bg-emerald/20 hover:bg-emerald/30 transition-all duration-300 hover:scale-110 backdrop-blur-sm"
                >
                  <User className="w-6 h-6 text-emerald" />
                </Link>
                <button
                  onClick={handleLogout}
                  className="p-3 rounded-2xl bg-coral/20 hover:bg-coral/30 transition-all duration-300 hover:scale-110 backdrop-blur-sm"
                >
                  <LogOut className="w-6 h-6 text-coral" />
                </button>
              </>
            ) : (
              <div className="flex items-center space-x-4">
                <Link
                  to="/login"
                  className="px-6 py-3 text-ocean hover:text-teal transition-colors font-semibold text-lg"
                >
                  Sign In
                </Link>
                <Link
                  to="/signup"
                  className="px-6 py-3 bg-coral-gradient hover:shadow-coral text-white rounded-2xl transition-all duration-300 transform hover:scale-105 font-semibold"
                >
                  Get Started
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;