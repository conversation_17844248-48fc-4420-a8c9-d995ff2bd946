import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from './LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading, error } = useAuth();

  // Show loading spinner while checking authentication
  if (loading) {
    return <LoadingSpinner />;
  }

  // If there's an authentication error, redirect to login
  if (error) {
    console.error('Authentication error in ProtectedRoute:', error);
    return <Navigate to="/login\" replace />;
  }

  // If no user is authenticated, redirect to login
  if (!user) {
    console.log('No authenticated user, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  // User is authenticated, render the protected content
  console.log('User authenticated, rendering protected content for:', user.email);
  return <>{children}</>;
};

export default ProtectedRoute;