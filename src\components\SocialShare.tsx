import React, { useState } from 'react';
import { Share2, Co<PERSON>, Check, ExternalLink, Heart } from 'lucide-react';

interface SocialShareProps {
  url: string;
  title: string;
  description?: string;
  imageUrl?: string;
  className?: string;
}

const SocialShare: React.FC<SocialShareProps> = ({ 
  url, 
  title, 
  description = '', 
  imageUrl = '',
  className = ''
}) => {
  const [copied, setCopied] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description);
  const encodedImage = encodeURIComponent(imageUrl);

  const socialPlatforms = [
    {
      name: 'Facebook',
      icon: '📘',
      color: 'bg-[#1877F2] hover:bg-[#166FE5]',
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedTitle}`,
      gradient: 'from-[#1877F2] to-[#166FE5]'
    },
    {
      name: 'Twitter (X)',
      icon: '🐦',
      color: 'bg-[#1DA1F2] hover:bg-[#1A91DA]',
      url: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}&via=Nostoria`,
      gradient: 'from-[#1DA1F2] to-[#1A91DA]'
    },
    {
      name: 'LinkedIn',
      icon: '💼',
      color: 'bg-[#0A66C2] hover:bg-[#095BA8]',
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
      gradient: 'from-[#0A66C2] to-[#095BA8]'
    },
    {
      name: 'WhatsApp',
      icon: '💬',
      color: 'bg-[#25D366] hover:bg-[#22C55E]',
      url: `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`,
      gradient: 'from-[#25D366] to-[#22C55E]'
    },
    {
      name: 'Telegram',
      icon: '✈️',
      color: 'bg-[#0088CC] hover:bg-[#007BB8]',
      url: `https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`,
      gradient: 'from-[#0088CC] to-[#007BB8]'
    },
    {
      name: 'Pinterest',
      icon: '📌',
      color: 'bg-[#E60023] hover:bg-[#D50020]',
      url: `https://pinterest.com/pin/create/button/?url=${encodedUrl}&description=${encodedTitle}&media=${encodedImage}`,
      gradient: 'from-[#E60023] to-[#D50020]'
    },
    {
      name: 'Reddit',
      icon: '🤖',
      color: 'bg-[#FF4500] hover:bg-[#E63E00]',
      url: `https://reddit.com/submit?url=${encodedUrl}&title=${encodedTitle}`,
      gradient: 'from-[#FF4500] to-[#E63E00]'
    },
    {
      name: 'TikTok',
      icon: '🎵',
      color: 'bg-[#000000] hover:bg-[#1a1a1a]',
      url: `https://www.tiktok.com/share?url=${encodedUrl}`,
      gradient: 'from-[#000000] to-[#1a1a1a]'
    }
  ];

  const handleShare = (platform: typeof socialPlatforms[0]) => {
    window.open(platform.url, '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes');
    setShowDropdown(false);
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title,
          text: description,
          url,
        });
        setShowDropdown(false);
      } catch (err) {
        console.error('Error sharing:', err);
      }
    } else {
      setShowDropdown(!showDropdown);
    }
  };

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={handleNativeShare}
        className="flex items-center space-x-2 bg-gradient-to-r from-sky-500 to-emerald-500 hover:from-sky-600 hover:to-emerald-600 text-white px-4 py-2 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
      >
        <Share2 className="w-4 h-4" />
        <span className="font-medium">Share</span>
      </button>

      {/* Dropdown Menu */}
      {showDropdown && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setShowDropdown(false)}
          />
          
          {/* Dropdown Content */}
          <div className="absolute top-full right-0 mt-2 w-80 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-2xl shadow-vibrant border border-white/20 dark:border-gray-700/50 z-50 overflow-hidden">
            <div className="p-6">
              <h3 className="text-lg font-serif font-semibold text-primary-700 dark:text-cream-100 mb-4 flex items-center space-x-2">
                <Heart className="w-5 h-5 text-coral-500" />
                <span>Share this memory</span>
              </h3>

              {/* Social Platform Grid */}
              <div className="grid grid-cols-4 gap-3 mb-6">
                {socialPlatforms.map((platform) => (
                  <button
                    key={platform.name}
                    onClick={() => handleShare(platform)}
                    className={`group relative p-3 rounded-xl ${platform.color} text-white transition-all duration-300 transform hover:scale-110 hover:shadow-lg`}
                    title={`Share on ${platform.name}`}
                  >
                    <div className="text-xl mb-1">{platform.icon}</div>
                    <div className="text-xs font-medium opacity-90 group-hover:opacity-100">
                      {platform.name.split(' ')[0]}
                    </div>
                    
                    {/* Hover gradient overlay */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${platform.gradient} opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-300`} />
                  </button>
                ))}
              </div>

              {/* Copy Link Section */}
              <div className="border-t border-cream-200 dark:border-gray-700 pt-4">
                <div className="flex items-center space-x-3">
                  <div className="flex-1 bg-cream-100 dark:bg-gray-800 rounded-lg px-3 py-2">
                    <div className="text-sm text-primary-600 dark:text-cream-300 truncate">
                      {url}
                    </div>
                  </div>
                  <button
                    onClick={handleCopyLink}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                      copied
                        ? 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400'
                        : 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 hover:bg-primary-200 dark:hover:bg-primary-800/50'
                    }`}
                  >
                    {copied ? (
                      <>
                        <Check className="w-4 h-4" />
                        <span>Copied!</span>
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4" />
                        <span>Copy</span>
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Additional Share Options */}
              <div className="mt-4 flex justify-center space-x-4">
                <button
                  onClick={() => {
                    const emailSubject = encodeURIComponent(`Check out this memory: ${title}`);
                    const emailBody = encodeURIComponent(`I wanted to share this beautiful memory with you:\n\n${title}\n\n${description}\n\nView it here: ${url}\n\nShared via Nostoria - Where Memories Live Forever`);
                    window.open(`mailto:?subject=${emailSubject}&body=${emailBody}`);
                  }}
                  className="flex items-center space-x-2 text-primary-600 dark:text-cream-300 hover:text-coral-500 dark:hover:text-coral-400 transition-colors"
                >
                  <span className="text-lg">📧</span>
                  <span className="text-sm font-medium">Email</span>
                </button>
                
                <button
                  onClick={() => {
                    const smsBody = encodeURIComponent(`Check out this memory: ${title} - ${url}`);
                    window.open(`sms:?body=${smsBody}`);
                  }}
                  className="flex items-center space-x-2 text-primary-600 dark:text-cream-300 hover:text-coral-500 dark:hover:text-coral-400 transition-colors"
                >
                  <span className="text-lg">💬</span>
                  <span className="text-sm font-medium">SMS</span>
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SocialShare;