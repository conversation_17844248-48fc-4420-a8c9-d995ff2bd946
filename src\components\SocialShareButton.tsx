import React from 'react';
import { ExternalLink } from 'lucide-react';

interface SocialShareButtonProps {
  platform: 'facebook' | 'twitter' | 'linkedin' | 'whatsapp' | 'telegram' | 'pinterest' | 'reddit' | 'tiktok' | 'instagram';
  url: string;
  title: string;
  description?: string;
  imageUrl?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const SocialShareButton: React.FC<SocialShareButtonProps> = ({
  platform,
  url,
  title,
  description = '',
  imageUrl = '',
  className = '',
  size = 'md'
}) => {
  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description);
  const encodedImage = encodeURIComponent(imageUrl);

  const platforms = {
    facebook: {
      name: 'Facebook',
      icon: '📘',
      color: 'bg-[#1877F2] hover:bg-[#166FE5]',
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedTitle}`,
      gradient: 'from-[#1877F2] to-[#166FE5]'
    },
    twitter: {
      name: 'Twitter (X)',
      icon: '🐦',
      color: 'bg-[#1DA1F2] hover:bg-[#1A91DA]',
      url: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}&via=Nostoria`,
      gradient: 'from-[#1DA1F2] to-[#1A91DA]'
    },
    linkedin: {
      name: 'LinkedIn',
      icon: '💼',
      color: 'bg-[#0A66C2] hover:bg-[#095BA8]',
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
      gradient: 'from-[#0A66C2] to-[#095BA8]'
    },
    whatsapp: {
      name: 'WhatsApp',
      icon: '💬',
      color: 'bg-[#25D366] hover:bg-[#22C55E]',
      url: `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`,
      gradient: 'from-[#25D366] to-[#22C55E]'
    },
    telegram: {
      name: 'Telegram',
      icon: '✈️',
      color: 'bg-[#0088CC] hover:bg-[#007BB8]',
      url: `https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`,
      gradient: 'from-[#0088CC] to-[#007BB8]'
    },
    pinterest: {
      name: 'Pinterest',
      icon: '📌',
      color: 'bg-[#E60023] hover:bg-[#D50020]',
      url: `https://pinterest.com/pin/create/button/?url=${encodedUrl}&description=${encodedTitle}&media=${encodedImage}`,
      gradient: 'from-[#E60023] to-[#D50020]'
    },
    reddit: {
      name: 'Reddit',
      icon: '🤖',
      color: 'bg-[#FF4500] hover:bg-[#E63E00]',
      url: `https://reddit.com/submit?url=${encodedUrl}&title=${encodedTitle}`,
      gradient: 'from-[#FF4500] to-[#E63E00]'
    },
    tiktok: {
      name: 'TikTok',
      icon: '🎵',
      color: 'bg-[#000000] hover:bg-[#1a1a1a]',
      url: `https://www.tiktok.com/share?url=${encodedUrl}`,
      gradient: 'from-[#000000] to-[#1a1a1a]'
    },
    instagram: {
      name: 'Instagram',
      icon: '📸',
      color: 'bg-gradient-to-r from-[#E4405F] to-[#5B51D8] hover:from-[#D73653] hover:to-[#5048C7]',
      url: `https://www.instagram.com/`, // Instagram doesn't support direct URL sharing
      gradient: 'from-[#E4405F] to-[#5B51D8]'
    }
  };

  const platformConfig = platforms[platform];
  
  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-10 h-10 text-sm',
    lg: 'w-12 h-12 text-base'
  };

  const iconSizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  const handleShare = () => {
    if (platform === 'instagram') {
      // For Instagram, we'll copy the URL and show a message
      navigator.clipboard.writeText(url);
      alert('Link copied! Open Instagram and paste it in your story or post.');
      return;
    }
    
    window.open(platformConfig.url, '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes');
  };

  return (
    <button
      onClick={handleShare}
      className={`group relative ${sizeClasses[size]} ${platformConfig.color} text-white rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-lg flex items-center justify-center ${className}`}
      title={`Share on ${platformConfig.name}`}
    >
      <span className={iconSizes[size]}>{platformConfig.icon}</span>
      
      {/* Hover gradient overlay */}
      <div className={`absolute inset-0 bg-gradient-to-br ${platformConfig.gradient} opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-300`} />
      
      {/* External link indicator for larger sizes */}
      {size === 'lg' && (
        <ExternalLink className="w-3 h-3 absolute -top-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      )}
    </button>
  );
};

export default SocialShareButton;