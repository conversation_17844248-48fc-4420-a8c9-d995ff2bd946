import React from 'react';
import { Crown, AlertTriangle, TrendingUp } from 'lucide-react';

interface SubscriptionInfo {
  plan_id: string;
  plan_name: string;
  memory_limit: number | null;
  memory_count: number;
  can_upload: boolean;
  status: string;
  current_period_end: string | null;
}

interface SubscriptionBannerProps {
  onUpgrade: () => void;
}

const SubscriptionBanner: React.FC<SubscriptionBannerProps> = ({ onUpgrade }) => {
  // Mock subscription info for build compatibility
  const subscriptionInfo: SubscriptionInfo | null = {
    plan_id: 'free',
    plan_name: 'Free',
    memory_limit: 50,
    memory_count: 25,
    can_upload: true,
    status: 'active',
    current_period_end: null
  };

  if (!subscriptionInfo || subscriptionInfo.plan_id === 'unlimited') {
    return null;
  }

  const memoryCount = subscriptionInfo.memory_count;
  const memoryLimit = subscriptionInfo.memory_limit || 50;
  const percentage = (memoryCount / memoryLimit) * 100;
  const isNearLimit = percentage >= 80;
  const isAtLimit = !subscriptionInfo.can_upload;

  if (percentage < 50) {
    return null; // Don't show banner until user is halfway to limit
  }

  return (
    <div className={`rounded-2xl p-6 mb-8 border ${
      isAtLimit 
        ? 'bg-coral-50 dark:bg-coral-900/20 border-coral-200 dark:border-coral-800'
        : isNearLimit
        ? 'bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800'
        : 'bg-sky-50 dark:bg-sky-900/20 border-sky-200 dark:border-sky-800'
    }`}>
      <div className="flex items-start space-x-4">
        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
          isAtLimit 
            ? 'bg-coral-100 dark:bg-coral-900/30'
            : isNearLimit
            ? 'bg-amber-100 dark:bg-amber-900/30'
            : 'bg-sky-100 dark:bg-sky-900/30'
        }`}>
          {isAtLimit ? (
            <AlertTriangle className="w-6 h-6 text-coral-600 dark:text-coral-400" />
          ) : isNearLimit ? (
            <TrendingUp className="w-6 h-6 text-amber-600 dark:text-amber-400" />
          ) : (
            <Crown className="w-6 h-6 text-sky-600 dark:text-sky-400" />
          )}
        </div>
        
        <div className="flex-1">
          <h3 className={`text-lg font-semibold mb-2 ${
            isAtLimit 
              ? 'text-coral-700 dark:text-coral-300'
              : isNearLimit
              ? 'text-amber-700 dark:text-amber-300'
              : 'text-sky-700 dark:text-sky-300'
          }`}>
            {isAtLimit 
              ? 'Memory Harbor Full!'
              : isNearLimit
              ? 'Approaching Memory Limit'
              : 'Upgrade for Unlimited Memories'
            }
          </h3>
          
          <p className={`mb-4 ${
            isAtLimit 
              ? 'text-coral-600 dark:text-coral-400'
              : isNearLimit
              ? 'text-amber-600 dark:text-amber-400'
              : 'text-sky-600 dark:text-sky-400'
          }`}>
            {isAtLimit 
              ? `You've used all ${memoryLimit} free memories. Upgrade to continue adding treasures to your collection.`
              : isNearLimit
              ? `You've used ${memoryCount} of ${memoryLimit} free memories. Upgrade for unlimited storage.`
              : `You've used ${memoryCount} of ${memoryLimit} free memories. Unlock unlimited memories with our premium plan.`
            }
          </p>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                isAtLimit 
                  ? 'bg-coral-500'
                  : isNearLimit
                  ? 'bg-amber-500'
                  : 'bg-sky-500'
              }`}
              style={{ width: `${Math.min(percentage, 100)}%` }}
            ></div>
          </div>

          <div className="flex items-center justify-between">
            <span className={`text-sm ${
              isAtLimit 
                ? 'text-coral-600 dark:text-coral-400'
                : isNearLimit
                ? 'text-amber-600 dark:text-amber-400'
                : 'text-sky-600 dark:text-sky-400'
            }`}>
              {memoryCount} / {memoryLimit} memories used
            </span>
            
            <button
              onClick={onUpgrade}
              className={`px-6 py-2 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 ${
                isAtLimit 
                  ? 'bg-coral-500 hover:bg-coral-600 text-white shadow-coral'
                  : isNearLimit
                  ? 'bg-amber-500 hover:bg-amber-600 text-white shadow-amber'
                  : 'bg-sky-500 hover:bg-sky-600 text-white shadow-sky'
              }`}
            >
              <span className="flex items-center space-x-2">
                <Crown className="w-4 h-4" />
                <span>Upgrade Now</span>
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionBanner;