import React, { useState } from 'react';
import { X, Check, Crown, Zap, Star, ExternalLink, CheckCircle } from 'lucide-react';

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  stripe_price_id_monthly: string | null;
  stripe_price_id_yearly: string | null;
  memory_limit: number | null;
  features: string[];
}

interface SubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentPlan?: string;
}

const SubscriptionModal: React.FC<SubscriptionModalProps> = ({ isOpen, onClose, currentPlan = 'free' }) => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('yearly');

  if (!isOpen) return null;

  // Mock plans data for build compatibility
  const plans: SubscriptionPlan[] = [
    {
      id: 'free',
      name: 'Free',
      description: 'Perfect for getting started',
      price_monthly: 0,
      price_yearly: 0,
      stripe_price_id_monthly: null,
      stripe_price_id_yearly: null,
      memory_limit: 50,
      features: ['50 memories', 'Basic sharing', 'Standard support']
    },
    {
      id: 'unlimited',
      name: 'Unlimited',
      description: 'For memory collectors',
      price_monthly: 1000,
      price_yearly: 12000,
      stripe_price_id_monthly: 'https://buy.stripe.com/monthly',
      stripe_price_id_yearly: 'https://buy.stripe.com/yearly',
      memory_limit: null,
      features: ['Unlimited memories', 'Advanced sharing', 'Priority support', 'Early access to features']
    }
  ];

  const unlimitedPlan = plans.find(p => p.id === 'unlimited');
  const freePlan = plans.find(p => p.id === 'free');

  if (!unlimitedPlan || !freePlan) {
    return null;
  }

  const monthlyPrice = unlimitedPlan.price_monthly / 100;
  const yearlyPrice = unlimitedPlan.price_yearly / 100;
  const yearlyMonthlyPrice = yearlyPrice / 12;
  const savings = Math.round(((monthlyPrice * 12 - yearlyPrice) / (monthlyPrice * 12)) * 100);

  const handleUpgrade = () => {
    // Get the payment link URL based on billing cycle
    const paymentLink = billingCycle === 'yearly' 
      ? unlimitedPlan.stripe_price_id_yearly 
      : unlimitedPlan.stripe_price_id_monthly;

    if (paymentLink && paymentLink.startsWith('https://buy.stripe.com/')) {
      // Open Stripe payment link in new tab
      window.open(paymentLink, '_blank');
    } else {
      alert('Payment link not configured. Please contact support.');
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-3xl shadow-vibrant max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-white/20">
        <div className="p-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h2 className="text-3xl font-serif font-bold text-primary-700 dark:text-cream-100 mb-2">
                Upgrade Your Memory Harbor
              </h2>
              <p className="text-primary-600 dark:text-cream-200 font-light">
                Unlock unlimited memories with instant activation
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-cream-100 dark:hover:bg-gray-800 rounded-full transition-colors"
            >
              <X className="w-6 h-6 text-primary-500 dark:text-cream-400" />
            </button>
          </div>

          {/* Billing Toggle */}
          <div className="flex justify-center mb-8">
            <div className="bg-cream-100 dark:bg-gray-800 p-1 rounded-2xl flex">
              <button
                onClick={() => setBillingCycle('monthly')}
                className={`px-6 py-3 rounded-xl font-medium transition-all ${
                  billingCycle === 'monthly'
                    ? 'bg-white dark:bg-gray-700 text-primary-700 dark:text-cream-100 shadow-soft'
                    : 'text-primary-500 dark:text-cream-400'
                }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setBillingCycle('yearly')}
                className={`px-6 py-3 rounded-xl font-medium transition-all relative ${
                  billingCycle === 'yearly'
                    ? 'bg-white dark:bg-gray-700 text-primary-700 dark:text-cream-100 shadow-soft'
                    : 'text-primary-500 dark:text-cream-400'
                }`}
              >
                Yearly
                <span className="absolute -top-2 -right-2 bg-coral-500 text-white text-xs px-2 py-1 rounded-full">
                  Save {savings}%
                </span>
              </button>
            </div>
          </div>

          {/* Plans */}
          <div className="grid md:grid-cols-2 gap-8">
            {/* Free Plan */}
            <div className={`relative p-8 rounded-2xl border-2 transition-all ${
              currentPlan === 'free'
                ? 'border-primary-500 bg-primary-50/50 dark:bg-primary-900/20'
                : 'border-cream-200 dark:border-gray-700 bg-white/50 dark:bg-gray-800/50'
            }`}>
              {currentPlan === 'free' && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Current Plan
                  </span>
                </div>
              )}
              
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Star className="w-8 h-8 text-primary-500" />
                </div>
                <h3 className="text-2xl font-serif font-bold text-primary-700 dark:text-cream-100 mb-2">
                  {freePlan.name}
                </h3>
                <div className="text-4xl font-bold text-primary-700 dark:text-cream-100 mb-2">
                  Free
                </div>
                <p className="text-primary-600 dark:text-cream-200 font-light">
                  {freePlan.description}
                </p>
              </div>

              <ul className="space-y-3 mb-8">
                {freePlan.features.map((feature, index) => (
                  <li key={index} className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-primary-500 flex-shrink-0" />
                    <span className="text-primary-600 dark:text-cream-200">{feature}</span>
                  </li>
                ))}
              </ul>

              <button
                disabled
                className="w-full py-3 bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded-xl font-medium cursor-not-allowed"
              >
                {currentPlan === 'free' ? 'Current Plan' : 'Downgrade Not Available'}
              </button>
            </div>

            {/* Unlimited Plan */}
            <div className="relative p-8 rounded-2xl border-2 border-coral-500 bg-gradient-to-br from-coral-50 to-sky-50 dark:from-coral-900/20 dark:to-sky-900/20">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-coral-500 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                  <Crown className="w-4 h-4" />
                  <span>Most Popular</span>
                </span>
              </div>
              
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-coral-100 dark:bg-coral-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-8 h-8 text-coral-500" />
                </div>
                <h3 className="text-2xl font-serif font-bold text-primary-700 dark:text-cream-100 mb-2">
                  {unlimitedPlan.name}
                </h3>
                <div className="text-4xl font-bold text-primary-700 dark:text-cream-100 mb-2">
                  £{billingCycle === 'yearly' ? yearlyMonthlyPrice.toFixed(0) : monthlyPrice}
                  <span className="text-lg font-normal text-primary-500 dark:text-cream-300">/month</span>
                </div>
                {billingCycle === 'yearly' && (
                  <p className="text-sm text-coral-600 dark:text-coral-400 font-medium">
                    Billed £{yearlyPrice} yearly
                  </p>
                )}
                <p className="text-primary-600 dark:text-cream-200 font-light mt-2">
                  {unlimitedPlan.description}
                </p>
              </div>

              <ul className="space-y-3 mb-8">
                {unlimitedPlan.features.map((feature, index) => (
                  <li key={index} className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-coral-500 flex-shrink-0" />
                    <span className="text-primary-600 dark:text-cream-200">{feature}</span>
                  </li>
                ))}
              </ul>

              {currentPlan === 'unlimited' ? (
                <button
                  disabled
                  className="w-full py-3 bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded-xl font-medium cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  <CheckCircle className="w-5 h-5" />
                  <span>Current Plan</span>
                </button>
              ) : (
                <button
                  onClick={handleUpgrade}
                  className="w-full py-3 bg-coral-gradient hover:shadow-coral text-white rounded-xl font-medium transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2"
                >
                  <ExternalLink className="w-5 h-5" />
                  <span>Upgrade Now</span>
                </button>
              )}
            </div>
          </div>

          {/* Instant Activation Notice */}
          <div className="mt-8 p-6 bg-emerald-50 dark:bg-emerald-900/20 rounded-2xl border border-emerald-200 dark:border-emerald-800">
            <h4 className="text-lg font-semibold text-emerald-700 dark:text-emerald-300 mb-3 flex items-center space-x-2">
              <Zap className="w-5 h-5" />
              <span>⚡ Fully Automated - No Waiting!</span>
            </h4>
            <div className="space-y-2 text-emerald-600 dark:text-emerald-400">
              <p>✅ <strong>Instant activation</strong> - Upload unlimited memories immediately after payment</p>
              <p>✅ <strong>No manual processing</strong> - Everything happens automatically</p>
              <p>✅ <strong>Secure Stripe payment</strong> - Your payment details are safe</p>
              <p>✅ <strong>Cancel anytime</strong> - Full control over your subscription</p>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-sm text-primary-500 dark:text-cream-400 mb-4">
              Secure payment powered by Stripe • Instant access • Cancel anytime
            </p>
            <div className="flex justify-center space-x-6 text-xs text-primary-400 dark:text-cream-500">
              <span>• 30-day money back</span>
              <span>• 24/7 support</span>
              <span>• Secure cloud storage</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionModal;