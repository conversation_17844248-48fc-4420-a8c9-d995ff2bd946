import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import type { User, AuthError } from '@supabase/supabase-js';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, name: string) => Promise<void>;
  logout: () => Promise<void>;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;

    const getSession = async () => {
      try {
        console.log('🔍 Checking for existing session...');
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('❌ Session error:', error);
          if (mounted) {
            setError(error.message);
            setUser(null);
          }
        } else if (session?.user) {
          console.log('✅ Found existing session for:', session.user.email);
          if (mounted) {
            setUser(session.user);
            setError(null);
          }
        } else {
          console.log('ℹ️ No existing session');
          if (mounted) {
            setUser(null);
            setError(null);
          }
        }
      } catch (err) {
        console.error('💥 Session check failed:', err);
        if (mounted) {
          setUser(null);
          setError('Failed to check authentication status');
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    getSession();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Auth state changed:', event, session?.user?.email || 'no user');
        
        if (mounted) {
          setUser(session?.user ?? null);
          setLoading(false);
          setError(null);
          
          if (event === 'SIGNED_IN') {
            console.log('✅ User signed in successfully');
          } else if (event === 'SIGNED_OUT') {
            console.log('👋 User signed out');
          } else if (event === 'TOKEN_REFRESHED') {
            console.log('🔄 Token refreshed');
          }
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🔐 Attempting login for:', email);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim().toLowerCase(),
        password: password,
      });

      if (error) {
        console.error('❌ Login error:', error);
        throw new Error(getAuthErrorMessage(error));
      }

      if (data.user) {
        console.log('✅ Login successful for:', data.user.email);
        setUser(data.user);
        setError(null);
      }
    } catch (error) {
      console.error('💥 Login failed:', error);
      const message = error instanceof Error ? error.message : 'Login failed';
      setError(message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signup = async (email: string, password: string, name: string) => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('📝 Attempting signup for:', email);
      
      const { data, error } = await supabase.auth.signUp({
        email: email.trim().toLowerCase(),
        password: password,
        options: {
          data: {
            display_name: name.trim(),
            full_name: name.trim(),
          },
        },
      });

      if (error) {
        console.error('❌ Signup error:', error);
        throw new Error(getAuthErrorMessage(error));
      }

      if (data.user) {
        console.log('✅ Signup successful for:', data.user.email);
        
        // Check if user is immediately confirmed (no email confirmation required)
        if (data.session) {
          console.log('✅ User automatically signed in after signup');
          setUser(data.user);
          setError(null);
        } else {
          console.log('ℹ️ User created but needs email confirmation');
          // For development, we'll treat this as success
          setUser(data.user);
          setError(null);
        }
      }
    } catch (error) {
      console.error('💥 Signup failed:', error);
      const message = error instanceof Error ? error.message : 'Account creation failed';
      setError(message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('👋 Attempting logout...');
      
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('❌ Logout error:', error);
        throw new Error(error.message);
      }
      
      console.log('✅ Logout successful');
      setUser(null);
      setError(null);
    } catch (error) {
      console.error('💥 Logout failed:', error);
      const message = error instanceof Error ? error.message : 'Logout failed';
      setError(message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    user,
    loading,
    login,
    signup,
    logout,
    error,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Helper function to get user-friendly error messages
function getAuthErrorMessage(error: AuthError): string {
  const message = error.message.toLowerCase();
  
  if (message.includes('invalid login credentials') || message.includes('invalid credentials')) {
    return 'Invalid email or password. Please check your credentials and try again.';
  }
  
  if (message.includes('email not confirmed')) {
    return 'Please check your email and click the confirmation link before signing in.';
  }
  
  if (message.includes('user already registered') || message.includes('already registered')) {
    return 'An account with this email already exists. Please sign in instead.';
  }
  
  if (message.includes('password should be at least')) {
    return 'Password must be at least 6 characters long.';
  }
  
  if (message.includes('invalid email') || message.includes('invalid format')) {
    return 'Please enter a valid email address.';
  }
  
  if (message.includes('signup is disabled')) {
    return 'Account creation is currently disabled. Please contact support.';
  }
  
  if (message.includes('too many requests')) {
    return 'Too many attempts. Please wait a moment and try again.';
  }
  
  if (message.includes('network') || message.includes('connection')) {
    return 'Network error. Please check your connection and try again.';
  }
  
  if (message.includes('fetch')) {
    return 'Connection error. Please check your internet connection and try again.';
  }
  
  // Return the original message if we don't have a specific handler
  return error.message || 'An authentication error occurred. Please try again.';
}