import { useState, useEffect } from 'react';
import { supabase, Memory } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

export const useMemories = () => {
  const [memories, setMemories] = useState<Memory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchMemories = async () => {
    if (!user) {
      setMemories([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('Fetching memories for user:', user.id);

      // Simple query without complex joins to avoid recursion
      const { data: memoriesData, error: memoriesError } = await supabase
        .from('memories')
        .select('*')
        .or(`user_id.eq.${user.id},visibility.eq.public`)
        .order('created_at', { ascending: false });

      if (memoriesError) {
        console.error('Error fetching memories:', memoriesError);
        throw memoriesError;
      }

      console.log('Fetched memories:', memoriesData?.length || 0);
      
      // If we have memories, fetch user profiles separately
      if (memoriesData && memoriesData.length > 0) {
        const userIds = [...new Set(memoriesData.map(m => m.user_id))];
        
        const { data: profiles, error: profilesError } = await supabase
          .from('user_profiles')
          .select('*')
          .in('id', userIds);

        if (profilesError) {
          console.warn('Could not fetch user profiles:', profilesError);
          // Continue without profiles rather than failing completely
        }

        // Combine the data
        const memoriesWithProfiles = memoriesData.map(memory => ({
          ...memory,
          user_profiles: profiles?.find(p => p.id === memory.user_id) || null
        }));

        setMemories(memoriesWithProfiles);
      } else {
        setMemories([]);
      }
    } catch (err) {
      console.error('Failed to fetch memories:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch memories');
    } finally {
      setLoading(false);
    }
  };

  const createMemory = async (memoryData: {
    title: string;
    description?: string;
    memory_type?: 'image' | 'video' | 'document';
    file_url?: string;
    thumbnail_url?: string;
    tags: string[];
    visibility: 'private' | 'public' | 'shared';
    memory_date: string;
  }) => {
    if (!user) throw new Error('User not authenticated');

    console.log('Creating memory with data:', memoryData);

    try {
      // Prepare the data for insertion
      const insertData = {
        user_id: user.id,
        title: memoryData.title,
        description: memoryData.description || null,
        memory_type: memoryData.memory_type || null,
        file_url: memoryData.file_url || null,
        thumbnail_url: memoryData.thumbnail_url || null,
        tags: memoryData.tags || [],
        visibility: memoryData.visibility,
        memory_date: memoryData.memory_date,
        view_count: 0
      };

      console.log('Insert data:', insertData);

      const { data, error } = await supabase
        .from('memories')
        .insert([insertData])
        .select('*')
        .single();

      if (error) {
        console.error('Error creating memory:', error);
        throw error;
      }

      console.log('Memory created successfully:', data);
      await fetchMemories(); // Refresh the list
      return data;
    } catch (error) {
      console.error('Failed to create memory:', error);
      throw error;
    }
  };

  const updateMemory = async (id: string, updates: Partial<Memory>) => {
    try {
      const { data, error } = await supabase
        .from('memories')
        .update(updates)
        .eq('id', id)
        .select('*')
        .single();

      if (error) throw error;

      await fetchMemories(); // Refresh the list
      return data;
    } catch (error) {
      console.error('Failed to update memory:', error);
      throw error;
    }
  };

  const deleteMemory = async (id: string) => {
    try {
      const { error } = await supabase
        .from('memories')
        .delete()
        .eq('id', id);

      if (error) throw error;

      await fetchMemories(); // Refresh the list
    } catch (error) {
      console.error('Failed to delete memory:', error);
      throw error;
    }
  };

  const incrementViewCount = async (id: string) => {
    try {
      const { error } = await supabase
        .rpc('increment_view_count', { memory_uuid: id });

      if (error) {
        console.error('Failed to increment view count:', error);
      }
    } catch (error) {
      console.error('Failed to increment view count:', error);
    }
  };

  useEffect(() => {
    fetchMemories();
  }, [user]);

  return {
    memories,
    loading,
    error,
    fetchMemories,
    createMemory,
    updateMemory,
    deleteMemory,
    incrementViewCount
  };
};