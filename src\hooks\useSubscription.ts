import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

export interface SubscriptionInfo {
  plan_id: string;
  plan_name: string;
  memory_limit: number | null;
  memory_count: number;
  can_upload: boolean;
  status: string;
  current_period_end: string | null;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  stripe_price_id_monthly: string | null;
  stripe_price_id_yearly: string | null;
  memory_limit: number | null;
  features: string[];
}

export const useSubscription = () => {
  const [subscriptionInfo, setSubscriptionInfo] = useState<SubscriptionInfo | null>(null);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchSubscriptionInfo = async () => {
    if (!user) {
      setSubscriptionInfo(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Call the database function to get subscription info
      const { data, error } = await supabase
        .rpc('get_subscription_info', { user_uuid: user.id });

      if (error) {
        console.error('Error fetching subscription info:', error);
        // Fallback to default free plan
        setSubscriptionInfo({
          plan_id: 'free',
          plan_name: 'Free',
          memory_limit: 50,
          memory_count: 0,
          can_upload: true,
          status: 'active',
          current_period_end: null
        });
      } else if (data && data.length > 0) {
        const info = data[0];
        setSubscriptionInfo({
          plan_id: info.plan_id,
          plan_name: info.plan_name,
          memory_limit: info.memory_limit,
          memory_count: parseInt(info.memory_count),
          can_upload: info.can_upload,
          status: info.status,
          current_period_end: info.current_period_end
        });
      } else {
        // Default to free plan
        setSubscriptionInfo({
          plan_id: 'free',
          plan_name: 'Free',
          memory_limit: 50,
          memory_count: 0,
          can_upload: true,
          status: 'active',
          current_period_end: null
        });
      }
    } catch (err) {
      console.error('Failed to fetch subscription info:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch subscription info');
      // Fallback to free plan
      setSubscriptionInfo({
        plan_id: 'free',
        plan_name: 'Free',
        memory_limit: 50,
        memory_count: 0,
        can_upload: true,
        status: 'active',
        current_period_end: null
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchPlans = async () => {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .order('price_monthly', { ascending: true });

      if (error) {
        console.error('Error fetching plans:', error);
        // Fallback to default plans
        setPlans([
          {
            id: 'free',
            name: 'Free',
            description: 'Perfect for getting started',
            price_monthly: 0,
            price_yearly: 0,
            stripe_price_id_monthly: null,
            stripe_price_id_yearly: null,
            memory_limit: 50,
            features: ['50 memories', 'Basic sharing', 'Standard support']
          },
          {
            id: 'unlimited',
            name: 'Unlimited',
            description: 'For memory collectors',
            price_monthly: 1000,
            price_yearly: 12000,
            stripe_price_id_monthly: 'https://buy.stripe.com/monthly',
            stripe_price_id_yearly: 'https://buy.stripe.com/yearly',
            memory_limit: null,
            features: ['Unlimited memories', 'Advanced sharing', 'Priority support', 'Early access to features']
          }
        ]);
      } else {
        setPlans(data || []);
      }
    } catch (err) {
      console.error('Failed to fetch plans:', err);
    }
  };

  useEffect(() => {
    fetchSubscriptionInfo();
    fetchPlans();
  }, [user]);

  return {
    subscriptionInfo,
    plans,
    loading,
    error,
    fetchSubscriptionInfo,
  };
};