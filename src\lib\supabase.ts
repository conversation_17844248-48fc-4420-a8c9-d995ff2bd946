import { createClient } from '@supabase/supabase-js';

// Get Supabase configuration from environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Validate required environment variables
if (!supabaseUrl) {
  throw new Error('Missing VITE_SUPABASE_URL environment variable. Please check your .env file.');
}

if (!supabaseAnonKey) {
  throw new Error('Missing VITE_SUPABASE_ANON_KEY environment variable. Please check your .env file.');
}

// Validate URL format
try {
  new URL(supabaseUrl);
} catch (error) {
  throw new Error('Invalid VITE_SUPABASE_URL format. Please provide a valid URL.');
}

console.log('🔧 Supabase URL:', supabaseUrl);
console.log('🔧 Supabase Key (first 20 chars):', supabaseAnonKey.substring(0, 20) + '...');

// Create Supabase client with optimal settings
export const supabase = createClient(supabaseUrl, supabase<PERSON>nonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    flowType: 'pkce',
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
    storageKey: 'nostoria-auth-token',
    debug: false
  },
  global: {
    headers: {
      'X-Client-Info': 'nostoria-web@1.0.0'
    }
  },
  db: {
    schema: 'public'
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// Test connection immediately
const testConnection = async () => {
  try {
    console.log('🔍 Testing Supabase connection...');
    
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.warn('⚠️ Session warning:', error.message);
    } else {
      console.log('✅ Supabase connection verified');
      if (data.session) {
        console.log('✅ Active session found for:', data.session.user.email);
      }
    }
  } catch (err) {
    console.error('❌ Connection test failed:', err);
  }
};

// Test on module load
if (typeof window !== 'undefined') {
  testConnection();
}

// Database types
export interface UserProfile {
  id: string;
  display_name: string;
  avatar_url?: string;
  bio?: string;
  created_at: string;
  updated_at: string;
}

export interface Memory {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  memory_type?: 'image' | 'video' | 'document';
  file_url?: string;
  thumbnail_url?: string;
  tags: string[];
  visibility: 'private' | 'public' | 'shared';
  memory_date: string;
  view_count: number;
  created_at: string;
  updated_at: string;
  user_profiles?: UserProfile;
}

export interface Comment {
  id: string;
  memory_id: string;
  user_id: string;
  comment_text: string;
  created_at: string;
  updated_at: string;
  user_profiles?: UserProfile;
}

export interface Reaction {
  id: string;
  memory_id: string;
  user_id: string;
  emoji: string;
  created_at: string;
  user_profiles?: UserProfile;
}

export interface MemoryCollaborator {
  id: string;
  memory_id: string;
  collaborator_id: string;
  role: 'viewer' | 'editor';
  invited_at: string;
  user_profiles?: UserProfile;
}