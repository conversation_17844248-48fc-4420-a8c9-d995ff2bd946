import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Upload, Search, Filter, Heart, Calendar, Tag, Share2, Eye, Sparkles, Camera, BookOpen, Anchor } from 'lucide-react';
import Navbar from '../components/Navbar';
import SubscriptionBanner from '../components/SubscriptionBanner';
import SubscriptionModal from '../components/SubscriptionModal';
import SocialShareButton from '../components/SocialShareButton';
import { useAuth } from '../contexts/AuthContext';
import { useMemories } from '../hooks/useMemories';
import { useProfile } from '../hooks/useProfile';
import { useSubscription } from '../hooks/useSubscription';
import LoadingSpinner from '../components/LoadingSpinner';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const { memories, loading: memoriesLoading } = useMemories();
  const { profile } = useProfile();
  const { subscriptionInfo } = useSubscription();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'image' | 'video' | 'document'>('all');
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);

  if (memoriesLoading) {
    return <LoadingSpinner />;
  }

  const filteredMemories = memories.filter(memory => {
    const matchesSearch = memory.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         memory.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         memory.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesFilter = filterType === 'all' || memory.memory_type === filterType;
    return matchesSearch && matchesFilter;
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'image': return '🏖️';
      case 'video': return '🎬';
      case 'document': return '📜';
      default: return '⚓';
    }
  };

  const userMemories = memories.filter(m => m.user_id === user?.id);
  const thisMonthMemories = userMemories.filter(m => 
    new Date(m.created_at).getMonth() === new Date().getMonth() &&
    new Date(m.created_at).getFullYear() === new Date().getFullYear()
  );
  const publicMemories = userMemories.filter(m => m.visibility === 'public');
  const totalViews = userMemories.reduce((sum, m) => sum + m.view_count, 0);

  const stats = [
    { label: 'Treasured Memories', value: userMemories.length, icon: Heart, color: 'text-coral-500', bgColor: 'bg-coral-100/50 dark:bg-coral-900/20' },
    { label: 'This Month', value: thisMonthMemories.length, icon: Calendar, color: 'text-secondary-500', bgColor: 'bg-secondary-100/50 dark:bg-secondary-900/20' },
    { label: 'Shared Voyages', value: publicMemories.length, icon: Share2, color: 'text-accent-500', bgColor: 'bg-accent-100/50 dark:bg-accent-900/20' },
    { label: 'Hearts Touched', value: totalViews, icon: Eye, color: 'text-primary-500', bgColor: 'bg-primary-100/50 dark:bg-primary-900/20' },
  ];

  return (
    <div className="min-h-screen">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Header */}
        <div className="mb-12 text-center">
          <div className="flex justify-center mb-6">
            <div className="relative">
              <div className="w-16 h-16 bg-gradient-to-br from-primary-200 to-secondary-200 rounded-full flex items-center justify-center shadow-coastal">
                <Anchor className="w-8 h-8 text-primary-600" />
              </div>
              <Sparkles className="w-4 h-4 text-coral-400 absolute -top-1 -right-1 animate-float" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-serif font-bold text-primary-700 dark:text-cream-100 mb-4">
            Welcome to your harbor, {profile?.display_name || user?.email?.split('@')[0]}!
          </h1>
          <p className="text-xl text-primary-600 dark:text-cream-200 font-light">
            Your memory collection shines like Mediterranean treasures.
          </p>
        </div>

        {/* Subscription Banner */}
        <SubscriptionBanner onUpgrade={() => setShowSubscriptionModal(true)} />

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white/70 dark:bg-gray-900/70 backdrop-blur-sm p-6 rounded-2xl shadow-soft hover:shadow-coastal transition-all duration-300 animate-slide-up border border-cream-200/50 dark:border-gray-700/50" style={{ animationDelay: `${index * 0.1}s` }}>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-primary-500 dark:text-cream-300 mb-2 font-light">{stat.label}</p>
                  <p className="text-3xl font-serif font-bold text-primary-700 dark:text-cream-100">{stat.value}</p>
                </div>
                <div className={`w-12 h-12 ${stat.bgColor} rounded-xl flex items-center justify-center`}>
                  <stat.icon className={`w-6 h-6 ${stat.color}`} />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Search and Filter */}
        <div className="bg-white/70 dark:bg-gray-900/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 mb-8 border border-cream-200/50 dark:border-gray-700/50">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search your coastal memories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 border border-primary-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-300 focus:border-transparent bg-cream-50/50 dark:bg-gray-800/50 text-primary-800 dark:text-cream-100 placeholder-primary-400 dark:placeholder-gray-400"
              />
            </div>
            <div className="relative">
              <Filter className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as any)}
                className="pl-12 pr-8 py-3 border border-primary-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-300 focus:border-transparent bg-cream-50/50 dark:bg-gray-800/50 text-primary-800 dark:text-cream-100 appearance-none"
              >
                <option value="all">All Treasures</option>
                <option value="image">Photos</option>
                <option value="video">Videos</option>
                <option value="document">Documents</option>
              </select>
            </div>
          </div>
        </div>

        {/* Memories Grid */}
        {filteredMemories.length === 0 ? (
          <div className="text-center py-20">
            <div className="flex justify-center mb-6">
              <div className="w-24 h-24 bg-gradient-to-br from-cream-200 to-primary-200 rounded-full flex items-center justify-center">
                <Camera className="w-12 h-12 text-primary-500" />
              </div>
            </div>
            <h3 className="text-3xl font-serif font-semibold text-primary-600 dark:text-cream-300 mb-4">
              {memories.length === 0 ? 'Your voyage begins here' : 'No memories found'}
            </h3>
            <p className="text-primary-500 dark:text-cream-400 mb-8 max-w-md mx-auto font-light leading-relaxed">
              {memories.length === 0 
                ? "Every beautiful journey starts with a single memory. Cast your first treasure into this digital harbor and watch your collection grow."
                : "Try adjusting your search to find the memory you're looking for."
              }
            </p>
            {memories.length === 0 && subscriptionInfo?.can_upload && (
              <Link
                to="/upload"
                className="inline-flex items-center space-x-2 bg-coastal hover:bg-sunset text-white px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-coastal font-medium"
              >
                <Upload className="w-5 h-5" />
                <span>Cast Your First Memory</span>
              </Link>
            )}
            {memories.length === 0 && !subscriptionInfo?.can_upload && (
              <button
                onClick={() => setShowSubscriptionModal(true)}
                className="inline-flex items-center space-x-2 bg-coral-gradient hover:shadow-coral text-white px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 font-medium"
              >
                <Upload className="w-5 h-5" />
                <span>Upgrade to Add Memories</span>
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredMemories.map((memory, index) => (
              <Link
                key={memory.id}
                to={`/memory/${memory.id}`}
                className="group bg-white/70 dark:bg-gray-900/70 backdrop-blur-sm rounded-2xl shadow-soft overflow-hidden hover:shadow-coastal transition-all duration-500 transform hover:-translate-y-2 animate-slide-up border border-cream-200/50 dark:border-gray-700/50"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="aspect-video bg-cream-100 dark:bg-gray-700 relative overflow-hidden">
                  {memory.thumbnail_url || memory.file_url ? (
                    <img
                      src={memory.thumbnail_url || memory.file_url}
                      alt={memory.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full text-4xl">
                      {getTypeIcon(memory.memory_type)}
                    </div>
                  )}
                  <div className="absolute top-3 right-3 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-primary-700 dark:text-cream-200 px-3 py-1 rounded-full text-xs font-medium">
                    {memory.memory_type}
                  </div>
                  {memory.visibility === 'private' && (
                    <div className="absolute top-3 left-3 bg-coral-400/90 text-white px-3 py-1 rounded-full text-xs font-medium">
                      Private
                    </div>
                  )}
                  
                  {/* Social Share Buttons for Public Memories */}
                  {memory.visibility === 'public' && (
                    <div className="absolute bottom-3 right-3 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <SocialShareButton
                        platform="facebook"
                        url={`${window.location.origin}/memory/${memory.id}`}
                        title={memory.title}
                        description={memory.description}
                        imageUrl={memory.thumbnail_url || memory.file_url}
                        size="sm"
                      />
                      <SocialShareButton
                        platform="twitter"
                        url={`${window.location.origin}/memory/${memory.id}`}
                        title={memory.title}
                        description={memory.description}
                        size="sm"
                      />
                    </div>
                  )}
                </div>
                
                <div className="p-6">
                  <h3 className="text-xl font-serif font-semibold text-primary-700 dark:text-cream-100 mb-3 group-hover:text-coral-600 dark:group-hover:text-coral-400 transition-colors">
                    {memory.title}
                  </h3>
                  <p className="text-primary-600 dark:text-cream-200 mb-4 line-clamp-2 font-light leading-relaxed">
                    {memory.description}
                  </p>
                  
                  <div className="flex flex-wrap gap-2 mb-4">
                    {memory.tags.slice(0, 3).map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="inline-flex items-center space-x-1 bg-secondary-100/70 dark:bg-secondary-900/30 text-secondary-600 dark:text-secondary-400 px-3 py-1 rounded-full text-xs font-medium"
                      >
                        <Tag className="w-3 h-3" />
                        <span>{tag}</span>
                      </span>
                    ))}
                    {memory.tags.length > 3 && (
                      <span className="text-xs text-primary-500 dark:text-cream-400 px-2 py-1">
                        +{memory.tags.length - 3} more
                      </span>
                    )}
                  </div>
                  
                  <div className="flex justify-between items-center text-sm text-primary-500 dark:text-cream-400">
                    <span className="font-light">{new Date(memory.memory_date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}</span>
                    <div className="flex items-center space-x-1">
                      <Eye className="w-4 h-4" />
                      <span>{memory.view_count}</span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}

        {/* Floating Add Button */}
        {subscriptionInfo?.can_upload ? (
          <Link
            to="/upload"
            className="fixed bottom-8 right-8 bg-coastal hover:bg-sunset text-white p-4 rounded-full shadow-coastal hover:shadow-ocean transition-all duration-300 transform hover:scale-110 z-50 group"
          >
            <Upload className="w-6 h-6 group-hover:scale-110 transition-transform" />
          </Link>
        ) : (
          <button
            onClick={() => setShowSubscriptionModal(true)}
            className="fixed bottom-8 right-8 bg-coral-gradient hover:shadow-coral text-white p-4 rounded-full transition-all duration-300 transform hover:scale-110 z-50 group"
          >
            <Upload className="w-6 h-6 group-hover:scale-110 transition-transform" />
          </button>
        )}
      </div>

      {/* Subscription Modal */}
      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={() => setShowSubscriptionModal(false)}
        currentPlan={subscriptionInfo?.plan_id}
      />
    </div>
  );
};

export default DashboardPage;