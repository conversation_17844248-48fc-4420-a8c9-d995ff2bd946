import React from 'react';
import { Link } from 'react-router-dom';
import { Heart, Upload, Share2, Users, Sparkles, ArrowRight, Camera, BookOpen, Star, Anchor, Zap, Globe, Eye, Calendar, Tag } from 'lucide-react';
import Navbar from '../components/Navbar';

const LandingPage: React.FC = () => {
  // Featured memories data
  const featuredMemories = [
    {
      id: '1',
      title: 'Summer Beach Vacation',
      description: 'Amazing family trip to the coast with beautiful sunsets and endless laughter.',
      type: 'image',
      thumbnail: 'https://images.pexels.com/photos/1032650/pexels-photo-1032650.jpeg?auto=compress&cs=tinysrgb&w=600',
      tags: ['family', 'vacation', 'beach'],
      date: 'Jul 15, 2024',
      views: 24,
      isPublic: true
    },
    {
      id: '2',
      title: 'Wedding Anniversary',
      description: 'Celebrating 10 years of love, laughter, and beautiful memories together.',
      type: 'video',
      thumbnail: 'https://images.pexels.com/photos/1024993/pexels-photo-1024993.jpeg?auto=compress&cs=tinysrgb&w=600',
      tags: ['anniversary', 'love', 'celebration'],
      date: 'Jun 20, 2024',
      views: 8,
      isPublic: false
    },
    {
      id: '3',
      title: 'Graduation Day',
      description: 'Finally finished my masters degree! So proud of this incredible achievement.',
      type: 'image',
      thumbnail: 'https://images.pexels.com/photos/267885/pexels-photo-267885.jpeg?auto=compress&cs=tinysrgb&w=600',
      tags: ['graduation', 'achievement', 'education'],
      date: 'May 18, 2024',
      views: 42,
      isPublic: true
    }
  ];

  return (
    <div className="min-h-screen">
      <Navbar />
      
      {/* Hero Section - Very compact */}
      <section className="relative overflow-hidden pt-12 pb-6">
        {/* Floating decorative elements */}
        <div className="absolute top-16 left-10 opacity-60">
          <div className="w-12 h-12 bg-sky rounded-full animate-float shadow-sky"></div>
        </div>
        <div className="absolute top-24 right-20 opacity-60">
          <div className="w-10 h-10 bg-emerald rounded-full animate-float shadow-emerald" style={{ animationDelay: '1s' }}></div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <div className="w-16 h-16 bg-ocean-gradient rounded-full flex items-center justify-center shadow-vibrant animate-pulse-slow">
                  <Heart className="w-8 h-8 text-white animate-bounce-gentle" />
                </div>
                <div className="absolute -top-1 -right-1">
                  <Sparkles className="w-4 h-4 text-coral animate-float" />
                </div>
              </div>
            </div>
            
            <h1 className="text-3xl md:text-4xl font-serif font-bold mb-3 leading-tight">
              <span className="bg-ocean-gradient bg-clip-text text-transparent">Where Memories</span><br />
              <span className="bg-coral-gradient bg-clip-text text-transparent">Live Forever</span>
            </h1>
            
            <p className="text-base md:text-lg text-ocean/80 mb-4 max-w-2xl mx-auto leading-relaxed font-light">
              Create a vibrant digital sanctuary where every precious moment sparkles like Mediterranean treasures.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center items-center mb-6">
              <Link
                to="/signup"
                className="group bg-ocean-gradient hover:shadow-vibrant text-white px-5 py-2.5 rounded-3xl text-base font-bold transition-all duration-300 transform hover:scale-110 flex items-center space-x-2"
              >
                <span>Begin Your Journey</span>
                <ArrowRight className="w-4 h-4 group-hover:translate-x-2 transition-transform" />
              </Link>
              <Link
                to="/login"
                className="px-5 py-2.5 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-ocean border-2 border-ocean/30 hover:border-ocean/50 rounded-3xl text-base font-bold transition-all duration-300 hover:scale-105"
              >
                Welcome Back
              </Link>
            </div>

            {/* Stats - Compact and above fold */}
            <div className="grid grid-cols-3 gap-4 max-w-lg mx-auto">
              {[
                { number: '10K+', label: 'Memories Preserved', color: 'text-sky' },
                { number: '500+', label: 'Happy Families', color: 'text-emerald' },
                { number: '50K+', label: 'Moments Shared', color: 'text-coral' },
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className={`text-2xl font-bold ${stat.color} mb-1`}>{stat.number}</div>
                  <div className="text-ocean/70 font-medium text-xs">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Memories Showcase - Above the fold */}
      <section className="py-6 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-6">
            <div className="flex justify-center mb-3">
              <div className="w-10 h-10 bg-coral-gradient rounded-full flex items-center justify-center shadow-coral">
                <Camera className="w-5 h-5 text-white" />
              </div>
            </div>
            <h2 className="text-xl md:text-2xl font-serif font-bold mb-3">
              <span className="bg-ocean-gradient bg-clip-text text-transparent">Treasured Moments</span><br />
              <span className="bg-coral-gradient bg-clip-text text-transparent">Come Alive</span>
            </h2>
            <p className="text-sm text-ocean/70 max-w-xl mx-auto font-light leading-relaxed">
              See how families around the world are preserving their most precious memories in vibrant detail.
            </p>
          </div>

          {/* Memory Cards Grid - Very compact for above fold */}
          <div className="grid md:grid-cols-3 gap-3">
            {featuredMemories.map((memory, index) => (
              <div
                key={memory.id}
                className="group bg-white/20 backdrop-blur-xl rounded-xl shadow-vibrant overflow-hidden hover:shadow-ocean transition-all duration-500 transform hover:-translate-y-1 border border-white/30"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Memory Image - Smaller aspect ratio */}
                <div className="aspect-[4/3] relative overflow-hidden">
                  <img
                    src={memory.thumbnail}
                    alt={memory.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  
                  {/* Type Badge */}
                  <div className="absolute top-1.5 left-1.5">
                    <div className="bg-white/90 backdrop-blur-sm text-ocean px-1.5 py-0.5 rounded-full text-xs font-medium flex items-center space-x-1">
                      {memory.type === 'image' && <Camera className="w-2.5 h-2.5" />}
                      {memory.type === 'video' && <div className="w-2.5 h-2.5 bg-coral rounded-full"></div>}
                      <span>{memory.type}</span>
                    </div>
                  </div>

                  {/* Privacy Badge */}
                  <div className="absolute top-1.5 right-1.5">
                    <div className={`px-1.5 py-0.5 rounded-full text-xs font-medium backdrop-blur-sm ${
                      memory.isPublic 
                        ? 'bg-emerald/20 text-emerald border border-emerald/30' 
                        : 'bg-coral/20 text-coral border border-coral/30'
                    }`}>
                      {memory.isPublic ? 'Public' : 'Private'}
                    </div>
                  </div>
                </div>

                {/* Memory Content - Very compact */}
                <div className="p-3">
                  <h3 className="text-sm font-serif font-bold text-ocean mb-1 group-hover:text-coral transition-colors line-clamp-1">
                    {memory.title}
                  </h3>
                  <p className="text-ocean/70 mb-2 line-clamp-2 font-light leading-relaxed text-xs">
                    {memory.description}
                  </p>

                  {/* Tags - Very compact */}
                  <div className="flex flex-wrap gap-1 mb-2">
                    {memory.tags.slice(0, 2).map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="inline-flex items-center space-x-0.5 bg-sky/20 text-sky px-1.5 py-0.5 rounded-full text-xs font-medium"
                      >
                        <Tag className="w-2 h-2" />
                        <span>{tag}</span>
                      </span>
                    ))}
                    {memory.tags.length > 2 && (
                      <span className="text-xs text-ocean/50 px-1">
                        +{memory.tags.length - 2}
                      </span>
                    )}
                  </div>

                  {/* Memory Stats - Very compact */}
                  <div className="flex justify-between items-center text-xs text-ocean/60">
                    <div className="flex items-center space-x-0.5">
                      <Calendar className="w-2.5 h-2.5" />
                      <span>{memory.date}</span>
                    </div>
                    <div className="flex items-center space-x-0.5">
                      <Eye className="w-2.5 h-2.5" />
                      <span>{memory.views}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Call to Action - Compact */}
          <div className="text-center mt-6">
            <Link
              to="/signup"
              className="inline-flex items-center space-x-2 bg-coral-gradient hover:shadow-vibrant text-white px-5 py-2.5 rounded-3xl text-base font-bold transition-all duration-300 transform hover:scale-110"
            >
              <Upload className="w-4 h-4" />
              <span>Start Creating Your Collection</span>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-32 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-6xl font-serif font-bold mb-8">
              <span className="bg-ocean-gradient bg-clip-text text-transparent">Every Memory Deserves</span><br />
              <span className="bg-coral-gradient bg-clip-text text-transparent">A Vibrant Home</span>
            </h2>
            <p className="text-2xl text-ocean/70 max-w-4xl mx-auto font-light leading-relaxed">
              Transform your precious moments into a living, breathing story that dances with Mediterranean colors.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: Upload,
                title: "Effortless Upload",
                description: "Drag, drop, and watch your memories come alive in brilliant color. Photos, videos, documents - all beautifully organized.",
                gradient: "bg-sky-gradient",
                shadow: "shadow-sky"
              },
              {
                icon: Zap,
                title: "Lightning Fast",
                description: "Experience blazing-fast performance with our modern architecture. Your memories load instantly, every time.",
                gradient: "bg-ocean-gradient",
                shadow: "shadow-ocean"
              },
              {
                icon: Share2,
                title: "Beautiful Sharing",
                description: "Create stunning memory galleries that captivate. Share with family or keep private - you're in control.",
                gradient: "bg-coral-gradient",
                shadow: "shadow-coral"
              },
              {
                icon: Globe,
                title: "Global Access",
                description: "Access your memories anywhere, anytime. Cloud-powered security meets beautiful design.",
                gradient: "bg-gradient-to-br from-emerald to-teal",
                shadow: "shadow-emerald"
              }
            ].map((feature, index) => (
              <div
                key={index}
                className={`group bg-white/10 backdrop-blur-xl p-8 rounded-3xl ${feature.shadow} hover:shadow-vibrant transition-all duration-500 transform hover:-translate-y-4 border border-white/20`}
              >
                <div className={`w-20 h-20 ${feature.gradient} rounded-3xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-serif font-bold text-ocean mb-4">
                  {feature.title}
                </h3>
                <p className="text-ocean/70 leading-relaxed font-light text-lg">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32">
        <div className="max-w-5xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-16 shadow-vibrant border border-white/20">
            <div className="flex justify-center mb-12">
              <div className="w-24 h-24 bg-coral-gradient rounded-full flex items-center justify-center shadow-coral">
                <BookOpen className="w-12 h-12 text-white" />
              </div>
            </div>
            <h2 className="text-5xl md:text-6xl font-serif font-bold mb-8">
              <span className="bg-ocean-gradient bg-clip-text text-transparent">Your Story Awaits</span>
            </h2>
            <p className="text-2xl text-ocean/70 mb-12 max-w-4xl mx-auto font-light leading-relaxed">
              Join thousands of families who trust Nostoria to keep their most precious moments safe, 
              beautiful, and vibrantly alive for generations to come.
            </p>
            <Link
              to="/signup"
              className="inline-flex items-center space-x-4 bg-ocean-gradient hover:shadow-vibrant text-white px-12 py-6 rounded-3xl text-2xl font-bold transition-all duration-300 transform hover:scale-110"
            >
              <Heart className="w-8 h-8" />
              <span>Start Creating Magic</span>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white/5 backdrop-blur-xl border-t border-white/20 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-4 mb-8 md:mb-0">
              <div className="w-12 h-12 bg-coral rounded-2xl flex items-center justify-center">
                <Heart className="w-7 h-7 text-white" />
              </div>
              <span className="text-2xl font-serif font-bold bg-ocean-gradient bg-clip-text text-transparent">Nostoria</span>
            </div>
            <p className="text-ocean/60 font-light text-lg">
              © 2024 Nostoria. Crafted with vibrant love for your memories.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;