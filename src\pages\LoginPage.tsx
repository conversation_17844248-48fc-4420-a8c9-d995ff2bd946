import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Heart, Mail, Lock, ArrowRight, Sparkles, AlertCircle, Eye, EyeOff } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!email.trim()) {
      setError('Please enter your email address');
      return;
    }
    
    if (!password) {
      setError('Please enter your password');
      return;
    }

    setLoading(true);
    setError('');

    try {
      console.log('🚀 Starting login process...');
      await login(email, password);
      console.log('✅ Login completed, redirecting...');
      
      // Small delay to ensure state is updated
      setTimeout(() => {
        navigate('/dashboard');
      }, 100);
    } catch (err) {
      console.error('❌ Login failed:', err);
      setError(err instanceof Error ? err.message : 'Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-12 bg-vibrant-mesh bg-[length:400%_400%] animate-gradient-shift">
      <div className="max-w-md w-full">
        <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-vibrant p-10 border border-white/20">
          <div className="text-center mb-10">
            <Link to="/" className="inline-flex items-center space-x-4 mb-10">
              <div className="relative">
                <div className="w-16 h-16 bg-coral rounded-3xl flex items-center justify-center shadow-coral">
                  <Heart className="w-9 h-9 text-white" />
                </div>
                <Sparkles className="w-5 h-5 text-emerald absolute -top-1 -right-1 animate-float" />
              </div>
              <span className="text-4xl font-serif font-bold bg-ocean-gradient bg-clip-text text-transparent">
                Nostoria
              </span>
            </Link>
            <h1 className="text-4xl font-serif font-bold text-ocean mb-4">
              Welcome Back
            </h1>
            <p className="text-xl text-ocean/70 font-light">
              Return to your vibrant memory sanctuary
            </p>
          </div>

          {error && (
            <div className="bg-coral/20 backdrop-blur-sm text-coral border border-coral/30 p-4 rounded-2xl mb-8 flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Sign In Failed</p>
                <p className="text-sm opacity-90">{error}</p>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-8">
            <div>
              <label className="block text-lg font-semibold text-ocean mb-3">
                Email Address
              </label>
              <div className="relative">
                <Mail className="absolute left-5 top-1/2 transform -translate-y-1/2 w-6 h-6 text-sky" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-14 pr-5 py-4 bg-white/10 backdrop-blur-sm border border-white/30 rounded-2xl focus:ring-4 focus:ring-sky/30 focus:border-sky text-ocean placeholder-ocean/50 text-lg transition-all"
                  placeholder="<EMAIL>"
                  required
                  disabled={loading}
                />
              </div>
            </div>

            <div>
              <label className="block text-lg font-semibold text-ocean mb-3">
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-5 top-1/2 transform -translate-y-1/2 w-6 h-6 text-sky" />
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-14 pr-14 py-4 bg-white/10 backdrop-blur-sm border border-white/30 rounded-2xl focus:ring-4 focus:ring-sky/30 focus:border-sky text-ocean placeholder-ocean/50 text-lg transition-all"
                  placeholder="••••••••"
                  required
                  disabled={loading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-5 top-1/2 transform -translate-y-1/2 text-sky hover:text-ocean transition-colors"
                  disabled={loading}
                >
                  {showPassword ? <EyeOff className="w-6 h-6" /> : <Eye className="w-6 h-6" />}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-ocean-gradient hover:shadow-vibrant disabled:opacity-50 text-white py-4 rounded-2xl font-bold text-xl flex items-center justify-center space-x-3 transition-all duration-300 transform hover:scale-105 disabled:transform-none"
            >
              {loading ? (
                <>
                  <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>Signing In...</span>
                </>
              ) : (
                <>
                  <span>Sign In</span>
                  <ArrowRight className="w-6 h-6" />
                </>
              )}
            </button>
          </form>

          <div className="mt-10 text-center">
            <p className="text-lg text-ocean/70 font-light">
              New to Nostoria?{' '}
              <Link to="/signup" className="text-coral hover:text-coral/80 font-semibold transition-colors">
                Begin your colorful journey
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;