import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { ArrowLeft, Heart, Tag, Calendar, Eye, Download, Globe, Lock, Sparkles } from 'lucide-react';
import Navbar from '../components/Navbar';
import SocialShare from '../components/SocialShare';
import { supabase, Memory } from '../lib/supabase';
import { useMemories } from '../hooks/useMemories';
import LoadingSpinner from '../components/LoadingSpinner';

const MemoryPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [memory, setMemory] = useState<Memory | null>(null);
  const [loading, setLoading] = useState(true);
  const [liked, setLiked] = useState(false);
  const [reactions, setReactions] = useState<any[]>([]);
  const { incrementViewCount } = useMemories();

  useEffect(() => {
    const fetchMemory = async () => {
      if (!id) return;

      try {
        setLoading(true);

        const { data, error } = await supabase
          .from('memories')
          .select(`
            *,
            user_profiles (
              display_name,
              avatar_url
            )
          `)
          .eq('id', id)
          .single();

        if (error) throw error;

        setMemory(data);
        
        // Increment view count
        await incrementViewCount(id);

        // Fetch reactions
        const { data: reactionsData } = await supabase
          .from('reactions')
          .select(`
            *,
            user_profiles (
              display_name
            )
          `)
          .eq('memory_id', id);

        setReactions(reactionsData || []);

      } catch (err) {
        console.error('Failed to fetch memory:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchMemory();
  }, [id, incrementViewCount]);

  const handleReaction = async (emoji: string) => {
    if (!memory) return;

    try {
      const { error } = await supabase
        .from('reactions')
        .upsert({
          memory_id: memory.id,
          emoji,
        });

      if (error) throw error;

      setLiked(!liked);
    } catch (err) {
      console.error('Failed to add reaction:', err);
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!memory) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-16 text-center">
          <h1 className="text-3xl font-serif font-bold text-primary-700 dark:text-cream-100 mb-4">
            Memory Not Found
          </h1>
          <p className="text-primary-600 dark:text-cream-200 mb-8">
            This memory might be private or no longer exist.
          </p>
          <Link
            to="/dashboard"
            className="inline-flex items-center space-x-2 bg-coastal hover:bg-sunset text-white px-6 py-3 rounded-full transition-colors shadow-coastal"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Back to Harbor</span>
          </Link>
        </div>
      </div>
    );
  }

  // Generate share URL and content
  const shareUrl = window.location.href;
  const shareTitle = `${memory.title} - Nostoria Memory`;
  const shareDescription = memory.description || `A beautiful memory shared on Nostoria - Where Memories Live Forever`;
  const shareImage = memory.thumbnail_url || memory.file_url || '';

  return (
    <div className="min-h-screen">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <Link
          to="/dashboard"
          className="inline-flex items-center space-x-2 text-primary-600 dark:text-cream-200 hover:text-coral-500 dark:hover:text-coral-400 mb-8 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Back to Harbor</span>
        </Link>

        {/* Memory Content */}
        <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl shadow-coastal overflow-hidden mb-8 border border-cream-200/50 dark:border-gray-700/50">
          {/* Media */}
          <div className="aspect-video bg-cream-100 dark:bg-gray-700 relative">
            {memory.file_url && (
              memory.memory_type === 'image' ? (
                <img
                  src={memory.file_url}
                  alt={memory.title}
                  className="w-full h-full object-cover"
                />
              ) : memory.memory_type === 'video' ? (
                <video
                  src={memory.file_url}
                  controls
                  className="w-full h-full object-cover"
                  poster={memory.thumbnail_url}
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="text-6xl mb-4">📜</div>
                    <p className="text-lg text-primary-600 dark:text-cream-200">Document</p>
                  </div>
                </div>
              )
            )}
            
            {/* Privacy Indicator */}
            <div className="absolute top-4 left-4">
              <div className={`flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm ${
                memory.visibility === 'public'
                  ? 'bg-accent-100/90 text-accent-700 dark:bg-accent-900/30 dark:text-accent-300'
                  : 'bg-coral-100/90 text-coral-700 dark:bg-coral-900/30 dark:text-coral-300'
              }`}>
                {memory.visibility === 'public' ? <Globe className="w-4 h-4" /> : <Lock className="w-4 h-4" />}
                <span>{memory.visibility === 'public' ? 'Public' : 'Private'}</span>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-8">
            <div className="flex justify-between items-start mb-6">
              <div className="flex-1">
                <h1 className="text-3xl font-serif font-bold text-primary-700 dark:text-cream-100 mb-4">
                  {memory.title}
                </h1>
                <div className="flex items-center space-x-6 text-sm text-primary-500 dark:text-cream-300 mb-6">
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(memory.memory_date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Eye className="w-4 h-4" />
                    <span>{memory.view_count} views</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span>by {memory.user_profiles?.display_name}</span>
                  </div>
                </div>
              </div>
              
              {/* Action Buttons */}
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => handleReaction('❤️')}
                  className={`p-3 rounded-full transition-colors ${
                    liked 
                      ? 'bg-coral-100 text-coral-600 dark:bg-coral-900/30 dark:text-coral-400'
                      : 'bg-cream-100 text-primary-600 hover:bg-cream-200 dark:bg-gray-800 dark:text-cream-300 dark:hover:bg-gray-700'
                  }`}
                >
                  <Heart className={`w-5 h-5 ${liked ? 'fill-current' : ''}`} />
                </button>
                
                {/* Social Share Component */}
                {memory.visibility === 'public' && (
                  <SocialShare
                    url={shareUrl}
                    title={shareTitle}
                    description={shareDescription}
                    imageUrl={shareImage}
                  />
                )}
                
                {memory.file_url && memory.memory_type !== 'video' && (
                  <a
                    href={memory.file_url}
                    download
                    className="p-3 bg-accent-100 text-accent-600 hover:bg-accent-200 dark:bg-accent-900/30 dark:text-accent-400 dark:hover:bg-accent-800/30 rounded-full transition-colors"
                  >
                    <Download className="w-5 h-5" />
                  </a>
                )}
              </div>
            </div>

            {/* Description */}
            {memory.description && (
              <div className="prose dark:prose-invert max-w-none mb-8">
                <p className="text-lg text-primary-600 dark:text-cream-200 leading-relaxed font-light">
                  {memory.description}
                </p>
              </div>
            )}

            {/* Tags */}
            {memory.tags.length > 0 && (
              <div className="flex flex-wrap gap-3 mb-6">
                {memory.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center space-x-1 bg-secondary-100/70 dark:bg-secondary-900/30 text-secondary-600 dark:text-secondary-400 px-3 py-2 rounded-full text-sm font-medium"
                  >
                    <Tag className="w-4 h-4" />
                    <span>{tag}</span>
                  </span>
                ))}
              </div>
            )}

            {/* Reactions */}
            {reactions.length > 0 && (
              <div className="border-t border-cream-200 dark:border-gray-700 pt-6">
                <h3 className="text-lg font-semibold text-primary-700 dark:text-cream-100 mb-4">
                  Reactions ({reactions.length})
                </h3>
                <div className="flex flex-wrap gap-2">
                  {reactions.map((reaction, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center space-x-1 bg-cream-100 dark:bg-gray-800 px-3 py-1 rounded-full text-sm"
                    >
                      <span>{reaction.emoji}</span>
                      <span className="text-primary-600 dark:text-cream-300">
                        {reaction.user_profiles?.display_name}
                      </span>
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Share Encouragement for Public Memories */}
            {memory.visibility === 'public' && (
              <div className="mt-8 p-6 bg-gradient-to-r from-sky-50 to-emerald-50 dark:from-sky-900/20 dark:to-emerald-900/20 rounded-2xl border border-sky-200 dark:border-sky-800">
                <h4 className="text-lg font-semibold text-primary-700 dark:text-cream-100 mb-3 flex items-center space-x-2">
                  <Sparkles className="w-5 h-5 text-coral-500" />
                  <span>Love this memory? Share it with the world!</span>
                </h4>
                <p className="text-primary-600 dark:text-cream-200 mb-4 font-light">
                  Help others discover this beautiful moment by sharing it on your favorite social platforms.
                </p>
                <SocialShare
                  url={shareUrl}
                  title={shareTitle}
                  description={shareDescription}
                  imageUrl={shareImage}
                  className="inline-block"
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MemoryPage;