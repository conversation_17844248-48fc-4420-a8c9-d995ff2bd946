import React, { useState } from 'react';
import { Camera, Edit3, Save, X, Heart, Calendar, Globe, Eye, Sparkles } from 'lucide-react';
import Navbar from '../components/Navbar';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../hooks/useProfile';
import { useMemories } from '../hooks/useMemories';
import LoadingSpinner from '../components/LoadingSpinner';

const ProfilePage: React.FC = () => {
  const { user } = useAuth();
  const { profile, updateProfile } = useProfile();
  const { memories } = useMemories();
  const [isEditing, setIsEditing] = useState(false);
  const [displayName, setDisplayName] = useState(profile?.display_name || '');
  const [bio, setBio] = useState(profile?.bio || '');
  const [saving, setSaving] = useState(false);

  if (!profile) {
    return <LoadingSpinner />;
  }

  const handleSave = async () => {
    try {
      setSaving(true);
      await updateProfile({
        display_name: displayName,
        bio: bio,
      });
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    } finally {
      setSaving(false);
    }
  };

  const userMemories = memories.filter(m => m.user_id === user?.id);
  const daysSinceJoined = Math.floor((Date.now() - new Date(profile.created_at).getTime()) / (1000 * 60 * 60 * 24));
  const publicMemories = userMemories.filter(m => m.visibility === 'public');
  const totalViews = userMemories.reduce((sum, m) => sum + m.view_count, 0);

  const stats = [
    { label: 'Memories', value: userMemories.length, icon: Heart, color: 'text-coral-500', bgColor: 'bg-coral-100/50 dark:bg-coral-900/20' },
    { label: 'Days Active', value: daysSinceJoined, icon: Calendar, color: 'text-secondary-500', bgColor: 'bg-secondary-100/50 dark:bg-secondary-900/20' },
    { label: 'Public Shares', value: publicMemories.length, icon: Globe, color: 'text-accent-500', bgColor: 'bg-accent-100/50 dark:bg-accent-900/20' },
    { label: 'Total Views', value: totalViews, icon: Eye, color: 'text-primary-500', bgColor: 'bg-primary-100/50 dark:bg-primary-900/20' },
  ];

  return (
    <div className="min-h-screen">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Profile Header */}
        <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl shadow-coastal overflow-hidden mb-8 border border-cream-200/50 dark:border-gray-700/50">
          <div className="h-32 bg-coastal"></div>
          <div className="relative px-8 pb-8">
            <div className="flex flex-col sm:flex-row items-start sm:items-end space-y-4 sm:space-y-0 sm:space-x-6 -mt-16">
              <div className="relative">
                <div className="w-32 h-32 bg-white dark:bg-gray-800 rounded-full border-4 border-white dark:border-gray-800 flex items-center justify-center text-4xl font-serif font-bold text-primary-500 shadow-coastal">
                  {profile.avatar_url ? (
                    <img src={profile.avatar_url} alt="Profile" className="w-full h-full rounded-full object-cover" />
                  ) : (
                    profile.display_name?.charAt(0).toUpperCase()
                  )}
                </div>
                <button className="absolute bottom-2 right-2 bg-coral-500 hover:bg-coral-600 text-white p-2 rounded-full transition-colors shadow-coral">
                  <Camera className="w-4 h-4" />
                </button>
                <Sparkles className="w-4 h-4 text-accent-400 absolute -top-1 -left-1 animate-float" />
              </div>
              
              <div className="flex-1 min-w-0">
                {isEditing ? (
                  <div className="space-y-4">
                    <input
                      type="text"
                      value={displayName}
                      onChange={(e) => setDisplayName(e.target.value)}
                      className="text-3xl font-serif font-bold bg-transparent border-b-2 border-primary-500 focus:outline-none text-primary-700 dark:text-cream-100 w-full"
                    />
                    <textarea
                      value={bio}
                      onChange={(e) => setBio(e.target.value)}
                      rows={2}
                      className="w-full text-primary-600 dark:text-cream-200 bg-cream-50 dark:bg-gray-800 rounded-lg p-3 focus:ring-2 focus:ring-primary-300 focus:outline-none resize-none border border-primary-200 dark:border-gray-600"
                      placeholder="Tell us about yourself..."
                    />
                    <div className="flex space-x-2">
                      <button
                        onClick={handleSave}
                        disabled={saving}
                        className="flex items-center space-x-1 bg-accent-500 hover:bg-accent-600 disabled:opacity-50 text-white px-4 py-2 rounded-lg transition-colors shadow-gentle"
                      >
                        <Save className="w-4 h-4" />
                        <span>{saving ? 'Saving...' : 'Save'}</span>
                      </button>
                      <button
                        onClick={() => {
                          setIsEditing(false);
                          setDisplayName(profile.display_name);
                          setBio(profile.bio || '');
                        }}
                        className="flex items-center space-x-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
                      >
                        <X className="w-4 h-4" />
                        <span>Cancel</span>
                      </button>
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="flex items-center space-x-3 mb-2">
                      <h1 className="text-3xl font-serif font-bold text-primary-700 dark:text-cream-100">
                        {profile.display_name}
                      </h1>
                      <button
                        onClick={() => {
                          setIsEditing(true);
                          setDisplayName(profile.display_name);
                          setBio(profile.bio || '');
                        }}
                        className="p-2 hover:bg-cream-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
                      >
                        <Edit3 className="w-5 h-5 text-primary-500 dark:text-cream-400" />
                      </button>
                    </div>
                    <p className="text-primary-600 dark:text-cream-200 mb-4 font-light">
                      {profile.bio || 'Capturing life\'s beautiful moments, one memory at a time.'}
                    </p>
                    <p className="text-sm text-primary-500 dark:text-cream-300">
                      Harbor member since {new Date(profile.created_at).toLocaleDateString('en-US', { 
                        month: 'long', 
                        year: 'numeric' 
                      })}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div 
              key={index} 
              className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm p-6 rounded-2xl shadow-soft text-center animate-slide-up border border-cream-200/50 dark:border-gray-700/50"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className={`w-12 h-12 ${stat.bgColor} rounded-xl flex items-center justify-center mx-auto mb-3`}>
                <stat.icon className={`w-6 h-6 ${stat.color}`} />
              </div>
              <div className="text-3xl font-serif font-bold text-primary-700 dark:text-cream-100 mb-1">
                {stat.value}
              </div>
              <div className="text-sm text-primary-600 dark:text-cream-300 font-light">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl shadow-coastal p-8 border border-cream-200/50 dark:border-gray-700/50">
          <h2 className="text-2xl font-serif font-semibold text-primary-700 dark:text-cream-100 mb-6">
            Recent Harbor Activity
          </h2>
          
          <div className="space-y-4">
            {userMemories.slice(0, 5).map((memory, index) => (
              <div key={memory.id} className="flex items-center space-x-4 p-4 rounded-lg hover:bg-cream-50 dark:hover:bg-gray-800 transition-colors">
                <div className="w-2 h-2 bg-coral-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-primary-700 dark:text-cream-100">
                    Cast a new memory <span className="font-semibold">"{memory.title}"</span>
                  </p>
                  <p className="text-sm text-primary-500 dark:text-cream-300">
                    {new Date(memory.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
            {userMemories.length === 0 && (
              <p className="text-primary-500 dark:text-cream-400 text-center py-8">
                No activity yet. Start by creating your first memory!
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;