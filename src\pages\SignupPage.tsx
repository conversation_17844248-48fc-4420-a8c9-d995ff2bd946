import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Heart, Mail, Lock, User, ArrowRight, Sparkles, AlertCircle, Eye, EyeOff, CheckCircle } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const SignupPage: React.FC = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { signup } = useAuth();
  const navigate = useNavigate();

  const validateForm = () => {
    if (!name.trim()) {
      setError('Please enter your full name');
      return false;
    }

    if (!email.trim()) {
      setError('Please enter your email address');
      return false;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setError('Please enter a valid email address');
      return false;
    }

    if (!password) {
      setError('Please enter a password');
      return false;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      console.log('🚀 Starting signup process...');
      await signup(email, password, name);
      console.log('✅ Signup completed, redirecting...');
      
      // Small delay to ensure state is updated
      setTimeout(() => {
        navigate('/dashboard');
      }, 100);
    } catch (err) {
      console.error('❌ Signup failed:', err);
      setError(err instanceof Error ? err.message : 'Account creation failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getPasswordStrength = (password: string) => {
    if (password.length === 0) return { strength: 0, label: '', color: '' };
    if (password.length < 6) return { strength: 1, label: 'Too short', color: 'text-coral' };
    if (password.length < 8) return { strength: 2, label: 'Weak', color: 'text-amber-500' };
    if (password.length < 12) return { strength: 3, label: 'Good', color: 'text-emerald' };
    return { strength: 4, label: 'Strong', color: 'text-emerald' };
  };

  const passwordStrength = getPasswordStrength(password);

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-12 bg-vibrant-mesh bg-[length:400%_400%] animate-gradient-shift">
      <div className="max-w-md w-full">
        <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-vibrant p-10 border border-white/20">
          <div className="text-center mb-10">
            <Link to="/" className="inline-flex items-center space-x-4 mb-10">
              <div className="relative">
                <div className="w-16 h-16 bg-emerald rounded-3xl flex items-center justify-center shadow-emerald">
                  <Heart className="w-9 h-9 text-white" />
                </div>
                <Sparkles className="w-5 h-5 text-coral absolute -top-1 -right-1 animate-float" />
              </div>
              <span className="text-4xl font-serif font-bold bg-ocean-gradient bg-clip-text text-transparent">
                Nostoria
              </span>
            </Link>
            <h1 className="text-4xl font-serif font-bold text-ocean mb-4">
              Begin Your Journey
            </h1>
            <p className="text-xl text-ocean/70 font-light">
              Create your vibrant memory sanctuary
            </p>
          </div>

          {error && (
            <div className="bg-coral/20 backdrop-blur-sm text-coral border border-coral/30 p-4 rounded-2xl mb-8 flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Account Creation Failed</p>
                <p className="text-sm opacity-90">{error}</p>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-lg font-semibold text-ocean mb-3">
                Full Name
              </label>
              <div className="relative">
                <User className="absolute left-5 top-1/2 transform -translate-y-1/2 w-6 h-6 text-emerald" />
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full pl-14 pr-5 py-4 bg-white/10 backdrop-blur-sm border border-white/30 rounded-2xl focus:ring-4 focus:ring-emerald/30 focus:border-emerald text-ocean placeholder-ocean/50 text-lg transition-all"
                  placeholder="Your beautiful name"
                  required
                  disabled={loading}
                />
              </div>
            </div>

            <div>
              <label className="block text-lg font-semibold text-ocean mb-3">
                Email Address
              </label>
              <div className="relative">
                <Mail className="absolute left-5 top-1/2 transform -translate-y-1/2 w-6 h-6 text-sky" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-14 pr-5 py-4 bg-white/10 backdrop-blur-sm border border-white/30 rounded-2xl focus:ring-4 focus:ring-sky/30 focus:border-sky text-ocean placeholder-ocean/50 text-lg transition-all"
                  placeholder="<EMAIL>"
                  required
                  disabled={loading}
                />
              </div>
            </div>

            <div>
              <label className="block text-lg font-semibold text-ocean mb-3">
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-5 top-1/2 transform -translate-y-1/2 w-6 h-6 text-teal" />
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-14 pr-14 py-4 bg-white/10 backdrop-blur-sm border border-white/30 rounded-2xl focus:ring-4 focus:ring-teal/30 focus:border-teal text-ocean placeholder-ocean/50 text-lg transition-all"
                  placeholder="••••••••"
                  required
                  disabled={loading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-5 top-1/2 transform -translate-y-1/2 text-teal hover:text-ocean transition-colors"
                  disabled={loading}
                >
                  {showPassword ? <EyeOff className="w-6 h-6" /> : <Eye className="w-6 h-6" />}
                </button>
              </div>
              {password && (
                <div className="mt-2 flex items-center space-x-2">
                  <div className="flex-1 bg-white/20 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${
                        passwordStrength.strength === 1 ? 'bg-coral w-1/4' :
                        passwordStrength.strength === 2 ? 'bg-amber-500 w-2/4' :
                        passwordStrength.strength === 3 ? 'bg-emerald w-3/4' :
                        passwordStrength.strength === 4 ? 'bg-emerald w-full' : 'w-0'
                      }`}
                    ></div>
                  </div>
                  <span className={`text-sm font-medium ${passwordStrength.color}`}>
                    {passwordStrength.label}
                  </span>
                </div>
              )}
            </div>

            <div>
              <label className="block text-lg font-semibold text-ocean mb-3">
                Confirm Password
              </label>
              <div className="relative">
                <Lock className="absolute left-5 top-1/2 transform -translate-y-1/2 w-6 h-6 text-coral" />
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="w-full pl-14 pr-14 py-4 bg-white/10 backdrop-blur-sm border border-white/30 rounded-2xl focus:ring-4 focus:ring-coral/30 focus:border-coral text-ocean placeholder-ocean/50 text-lg transition-all"
                  placeholder="••••••••"
                  required
                  disabled={loading}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-5 top-1/2 transform -translate-y-1/2 text-coral hover:text-ocean transition-colors"
                  disabled={loading}
                >
                  {showConfirmPassword ? <EyeOff className="w-6 h-6" /> : <Eye className="w-6 h-6" />}
                </button>
              </div>
              {confirmPassword && password && (
                <div className="mt-2 flex items-center space-x-2">
                  {password === confirmPassword ? (
                    <div className="flex items-center space-x-1 text-emerald">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">Passwords match</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1 text-coral">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm">Passwords don't match</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-coral-gradient hover:shadow-vibrant disabled:opacity-50 text-white py-4 rounded-2xl font-bold text-xl flex items-center justify-center space-x-3 transition-all duration-300 transform hover:scale-105 disabled:transform-none"
            >
              {loading ? (
                <>
                  <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>Creating Your Sanctuary...</span>
                </>
              ) : (
                <>
                  <span>Create Account</span>
                  <ArrowRight className="w-6 h-6" />
                </>
              )}
            </button>
          </form>

          <div className="mt-10 text-center">
            <p className="text-lg text-ocean/70 font-light">
              Already have an account?{' '}
              <Link to="/login" className="text-emerald hover:text-emerald/80 font-semibold transition-colors">
                Welcome back
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignupPage;