import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload, X, Tag, Calendar, Globe, Lock, Image, Video, FileText, Sparkles, Anchor, AlertCircle, CheckCircle, Crown } from 'lucide-react';
import Navbar from '../components/Navbar';
import SubscriptionModal from '../components/SubscriptionModal';
import { useMemories } from '../hooks/useMemories';
import { useSubscription } from '../hooks/useSubscription';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

const UploadPage: React.FC = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [visibility, setVisibility] = useState<'private' | 'public' | 'shared'>('private');
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStep, setUploadStep] = useState('');
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();
  const { createMemory } = useMemories();
  const { subscriptionInfo } = useSubscription();
  const { user } = useAuth();

  // Check if user can upload when component mounts
  useEffect(() => {
    if (subscriptionInfo && !subscriptionInfo.can_upload) {
      setShowSubscriptionModal(true);
    }
  }, [subscriptionInfo]);

  const handleFileSelect = (selectedFiles: FileList | null) => {
    if (!selectedFiles) return;
    
    // Check if user can upload
    if (subscriptionInfo && !subscriptionInfo.can_upload) {
      setShowSubscriptionModal(true);
      return;
    }
    
    const newFiles = Array.from(selectedFiles).filter(file => {
      const isImage = file.type.startsWith('image/');
      const isVideo = file.type.startsWith('video/');
      const isDocument = file.type === 'application/pdf' || 
                        file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                        file.type === 'text/plain';
      
      return isImage || isVideo || isDocument;
    });
    
    setFiles(prev => [...prev, ...newFiles]);
    setError(null);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags(prev => [...prev, tagInput.trim()]);
      setTagInput('');
    }
  };

  const removeTag = (tag: string) => {
    setTags(prev => prev.filter(t => t !== tag));
  };

  const uploadFile = async (file: File): Promise<{ url: string; thumbnailUrl?: string }> => {
    try {
      setUploadStep('Preparing file upload...');
      setUploadProgress(10);

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Generate unique filename with user folder structure
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExt}`;
      
      setUploadStep('Uploading to storage...');
      setUploadProgress(30);

      // Upload file to Supabase Storage
      const { data, error } = await supabase.storage
        .from('memories')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Storage upload error:', error);
        
        // Provide specific error messages
        if (error.message.includes('Bucket not found')) {
          throw new Error('Storage not configured. Please contact support.');
        } else if (error.message.includes('not allowed') || error.message.includes('policy')) {
          throw new Error('Upload permission denied. Please try logging out and back in.');
        } else if (error.message.includes('size')) {
          throw new Error('File too large. Maximum size is 50MB.');
        } else if (error.message.includes('mime') || error.message.includes('type')) {
          throw new Error('File type not supported. Please use images, videos, or documents.');
        } else {
          throw new Error(`Upload failed: ${error.message}`);
        }
      }

      setUploadStep('Generating public URL...');
      setUploadProgress(80);

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('memories')
        .getPublicUrl(data.path);

      // For images, use the same URL as thumbnail
      let thumbnailUrl;
      if (file.type.startsWith('image/')) {
        thumbnailUrl = publicUrl;
      }

      setUploadProgress(100);
      setUploadStep('Upload complete!');

      return { url: publicUrl, thumbnailUrl };
    } catch (error) {
      console.error('Upload error:', error);
      throw error;
    }
  };

  const getMemoryType = (file: File): 'image' | 'video' | 'document' => {
    if (file.type.startsWith('image/')) return 'image';
    if (file.type.startsWith('video/')) return 'video';
    return 'document';
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Check if user can upload
    if (subscriptionInfo && !subscriptionInfo.can_upload) {
      setShowSubscriptionModal(true);
      return;
    }
    
    if (files.length === 0) {
      setError('Please select at least one file to upload');
      return;
    }
    
    if (!title.trim()) {
      setError('Please enter a title for your memory');
      return;
    }

    if (!user) {
      setError('You must be logged in to upload memories');
      return;
    }

    setUploading(true);
    setError(null);
    setUploadProgress(0);
    setUploadStep('Starting upload...');
    
    try {
      // Upload the first file (for simplicity, handling single file upload)
      const primaryFile = files[0];
      
      // Check file size (limit to 50MB)
      if (primaryFile.size > 50 * 1024 * 1024) {
        throw new Error('File size must be less than 50MB');
      }

      const { url, thumbnailUrl } = await uploadFile(primaryFile);
      
      setUploadStep('Creating memory record...');

      // Create memory record in database
      const memoryData = {
        title: title.trim(),
        description: description.trim() || undefined,
        memory_type: getMemoryType(primaryFile),
        file_url: url,
        thumbnail_url: thumbnailUrl || undefined,
        tags,
        visibility,
        memory_date: date,
      };

      await createMemory(memoryData);

      setUploadStep('Success! Redirecting...');
      
      // Small delay to show success message
      setTimeout(() => {
        navigate('/dashboard');
      }, 1000);
      
    } catch (error) {
      console.error('Upload failed:', error);
      
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('An unexpected error occurred. Please try again.');
      }
    } finally {
      setUploading(false);
      setUploadProgress(0);
      setUploadStep('');
    }
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return <Image className="w-8 h-8 text-primary-500" />;
    if (file.type.startsWith('video/')) return <Video className="w-8 h-8 text-secondary-500" />;
    return <FileText className="w-8 h-8 text-accent-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Show upgrade prompt if user can't upload
  if (subscriptionInfo && !subscriptionInfo.can_upload) {
    return (
      <div className="min-h-screen">
        <Navbar />
        
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-20">
            <div className="flex justify-center mb-6">
              <div className="w-24 h-24 bg-coral-100 dark:bg-coral-900/30 rounded-full flex items-center justify-center">
                <Crown className="w-12 h-12 text-coral-500" />
              </div>
            </div>
            <h1 className="text-4xl font-serif font-bold text-primary-700 dark:text-cream-100 mb-4">
              Memory Harbor Full
            </h1>
            <p className="text-xl text-primary-600 dark:text-cream-200 mb-8 max-w-2xl mx-auto font-light leading-relaxed">
              You've used all {subscriptionInfo.memory_limit} free memories. Upgrade to unlimited to continue preserving your precious moments.
            </p>
            <button
              onClick={() => setShowSubscriptionModal(true)}
              className="inline-flex items-center space-x-2 bg-coral-gradient hover:shadow-coral text-white px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 font-medium"
            >
              <Crown className="w-5 h-5" />
              <span>Upgrade to Unlimited</span>
            </button>
          </div>
        </div>

        <SubscriptionModal
          isOpen={showSubscriptionModal}
          onClose={() => {
            setShowSubscriptionModal(false);
            navigate('/dashboard');
          }}
          currentPlan={subscriptionInfo?.plan_id}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-12 text-center">
          <div className="flex justify-center mb-6">
            <div className="relative">
              <div className="w-16 h-16 bg-gradient-to-br from-primary-200 to-secondary-200 rounded-full flex items-center justify-center shadow-coastal">
                <Anchor className="w-8 h-8 text-primary-600" />
              </div>
              <Sparkles className="w-4 h-4 text-coral-400 absolute -top-1 -right-1 animate-float" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-serif font-bold text-primary-700 dark:text-cream-100 mb-4">
            Cast a New Memory
          </h1>
          <p className="text-xl text-primary-600 dark:text-cream-200 font-light">
            Add another precious treasure to your coastal sanctuary.
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-8 bg-coral-50 dark:bg-coral-900/20 border border-coral-200 dark:border-coral-800 rounded-2xl p-6">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-coral-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <AlertCircle className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-coral-700 dark:text-coral-300 mb-1">
                  Upload Failed
                </h3>
                <p className="text-coral-600 dark:text-coral-400 leading-relaxed">
                  {error}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Upload Progress */}
        {uploading && (
          <div className="mb-8 bg-sky-50 dark:bg-sky-900/20 border border-sky-200 dark:border-sky-800 rounded-2xl p-6">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-6 h-6 bg-sky-500 rounded-full flex items-center justify-center">
                {uploadProgress === 100 ? (
                  <CheckCircle className="w-4 h-4 text-white" />
                ) : (
                  <Upload className="w-4 h-4 text-white animate-bounce" />
                )}
              </div>
              <h3 className="text-lg font-semibold text-sky-700 dark:text-sky-300">
                {uploadStep || 'Preserving Your Memory...'}
              </h3>
            </div>
            <div className="w-full bg-sky-200 dark:bg-sky-800 rounded-full h-2">
              <div 
                className="bg-sky-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-sky-600 dark:text-sky-400 mt-2">
              {uploadProgress}% complete
            </p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* File Upload Area */}
          <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl shadow-coastal p-8 border border-cream-200/50 dark:border-gray-700/50">
            <h2 className="text-2xl font-serif font-semibold text-primary-700 dark:text-cream-100 mb-6">
              Upload Your Treasures
            </h2>
            
            <div
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              className="border-2 border-dashed border-primary-300 dark:border-primary-600 rounded-xl p-12 text-center hover:border-coral-400 transition-colors cursor-pointer bg-cream-50/50 dark:bg-gray-800/50"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="w-16 h-16 text-coral-400 mx-auto mb-4" />
              <p className="text-xl text-primary-600 dark:text-cream-200 mb-2">
                Gently place your memories here
              </p>
              <p className="text-primary-500 dark:text-cream-300 mb-4 font-light">
                or click to browse your collection
              </p>
              <p className="text-sm text-primary-400 dark:text-cream-400">
                Supports: Images (JPG, PNG, GIF, WebP), Videos (MP4, MOV, AVI, WebM), Documents (PDF, DOCX, TXT)
              </p>
              <p className="text-xs text-primary-400 dark:text-cream-400 mt-2">
                Maximum file size: 50MB
              </p>
            </div>
            
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*,video/*,.pdf,.docx,.txt"
              onChange={(e) => handleFileSelect(e.target.files)}
              className="hidden"
            />

            {files.length > 0 && (
              <div className="mt-6 space-y-3">
                <h3 className="font-semibold text-primary-700 dark:text-cream-100">Selected Treasures:</h3>
                {files.map((file, index) => (
                  <div key={index} className="flex items-center justify-between bg-cream-50 dark:bg-gray-800 p-4 rounded-lg border border-cream-200 dark:border-gray-700">
                    <div className="flex items-center space-x-3">
                      {getFileIcon(file)}
                      <div>
                        <p className="font-medium text-primary-700 dark:text-cream-100">{file.name}</p>
                        <p className="text-sm text-primary-500 dark:text-cream-300">{formatFileSize(file.size)}</p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeFile(index)}
                      className="p-1 hover:bg-coral-100 dark:hover:bg-coral-900/30 rounded-full transition-colors"
                    >
                      <X className="w-5 h-5 text-coral-500" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Memory Details */}
          <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl shadow-coastal p-8 border border-cream-200/50 dark:border-gray-700/50">
            <h2 className="text-2xl font-serif font-semibold text-primary-700 dark:text-cream-100 mb-6">
              Memory Details
            </h2>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-primary-700 dark:text-cream-200 mb-2">
                  Title *
                </label>
                <input
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Give your memory a beautiful title..."
                  className="w-full px-4 py-3 border border-primary-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-300 focus:border-transparent bg-cream-50/50 dark:bg-gray-800/50 text-primary-800 dark:text-cream-100 placeholder-primary-400 dark:placeholder-gray-400"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-700 dark:text-cream-200 mb-2">
                  Description
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Tell the story behind this precious memory..."
                  rows={4}
                  className="w-full px-4 py-3 border border-primary-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-300 focus:border-transparent bg-cream-50/50 dark:bg-gray-800/50 text-primary-800 dark:text-cream-100 placeholder-primary-400 dark:placeholder-gray-400 resize-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-700 dark:text-cream-200 mb-2">
                  Tags
                </label>
                <div className="flex flex-wrap gap-2 mb-3">
                  {tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center space-x-1 bg-secondary-100/70 dark:bg-secondary-900/30 text-secondary-600 dark:text-secondary-400 px-3 py-1 rounded-full text-sm"
                    >
                      <Tag className="w-3 h-3" />
                      <span>{tag}</span>
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="ml-1 hover:text-coral-600 dark:hover:text-coral-400"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    placeholder="Add a tag..."
                    className="flex-1 px-4 py-2 border border-primary-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-300 focus:border-transparent bg-cream-50/50 dark:bg-gray-800/50 text-primary-800 dark:text-cream-100 placeholder-primary-400 dark:placeholder-gray-400"
                  />
                  <button
                    type="button"
                    onClick={addTag}
                    className="px-4 py-2 bg-secondary-500 hover:bg-secondary-600 text-white rounded-lg transition-colors"
                  >
                    Add
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-primary-700 dark:text-cream-200 mb-2">
                    <Calendar className="w-4 h-4 inline mr-1" />
                    Memory Date
                  </label>
                  <input
                    type="date"
                    value={date}
                    onChange={(e) => setDate(e.target.value)}
                    className="w-full px-4 py-3 border border-primary-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-300 focus:border-transparent bg-cream-50/50 dark:bg-gray-800/50 text-primary-800 dark:text-cream-100"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-primary-700 dark:text-cream-200 mb-2">
                    Privacy
                  </label>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="privacy"
                        checked={visibility === 'public'}
                        onChange={() => setVisibility('public')}
                        className="sr-only"
                      />
                      <div className={`flex items-center space-x-2 px-4 py-3 rounded-xl border-2 cursor-pointer transition-colors ${
                        visibility === 'public'
                          ? 'border-accent-500 bg-accent-50 dark:bg-accent-900/20' 
                          : 'border-primary-200 dark:border-gray-600'
                      }`}>
                        <Globe className="w-4 h-4" />
                        <span>Public</span>
                      </div>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="privacy"
                        checked={visibility === 'private'}
                        onChange={() => setVisibility('private')}
                        className="sr-only"
                      />
                      <div className={`flex items-center space-x-2 px-4 py-3 rounded-xl border-2 cursor-pointer transition-colors ${
                        visibility === 'private'
                          ? 'border-coral-500 bg-coral-50 dark:bg-coral-900/20' 
                          : 'border-primary-200 dark:border-gray-600'
                      }`}>
                        <Lock className="w-4 h-4" />
                        <span>Private</span>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => navigate('/dashboard')}
              disabled={uploading}
              className="px-8 py-3 border border-primary-300 dark:border-gray-600 text-primary-600 dark:text-cream-200 rounded-xl hover:bg-cream-50 dark:hover:bg-gray-800 transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={uploading || files.length === 0 || !title.trim()}
              className="px-8 py-3 bg-coastal hover:bg-sunset disabled:from-primary-300 disabled:to-secondary-300 text-white rounded-xl transition-all duration-300 transform hover:scale-[1.02] disabled:transform-none shadow-coastal disabled:opacity-50"
            >
              {uploading ? 'Preserving Memory...' : 'Cast Into Harbor'}
            </button>
          </div>
        </form>
      </div>

      {/* Subscription Modal */}
      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={() => setShowSubscriptionModal(false)}
        currentPlan={subscriptionInfo?.plan_id}
      />
    </div>
  );
};

export default UploadPage;