import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import Stripe from 'https://esm.sh/stripe@14.25.0'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
)

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, stripe-signature',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const body = await req.text()
    const signature = req.headers.get('stripe-signature')

    if (!signature) {
      throw new Error('No Stripe signature found')
    }

    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      Deno.env.get('STRIPE_WEBHOOK_SECRET') || ''
    )

    console.log(`🎯 Processing webhook event: ${event.type}`)

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session
        
        console.log('💳 Payment completed via payment link!')
        console.log('Customer email:', session.customer_details?.email)
        console.log('Amount:', session.amount_total)
        
        if (session.customer_details?.email && session.payment_intent) {
          const { error } = await supabaseClient.rpc('handle_checkout_completed', {
            checkout_session_id: session.id,
            customer_email: session.customer_details.email,
            payment_intent_id: session.payment_intent as string,
            amount_total: session.amount_total || 0,
            currency: session.currency || 'gbp'
          })
          
          if (error) {
            console.error('❌ Error processing payment:', error)
            throw error
          }
          
          console.log('✅ Subscription activated automatically!')
        }
        break
      }

      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        
        console.log('💰 Payment intent succeeded')
        console.log('Amount:', paymentIntent.amount)
        
        // Additional logging for payment links
        if (paymentIntent.metadata?.source === 'payment_link') {
          console.log('🔗 Payment came from payment link')
        }
        break
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        
        console.log('📄 Invoice payment succeeded')
        
        // Handle recurring payments if using subscriptions later
        if (invoice.subscription) {
          console.log('🔄 Recurring payment processed')
        }
        break
      }

      default:
        console.log(`ℹ️ Unhandled event type: ${event.type}`)
    }

    return new Response(
      JSON.stringify({ 
        received: true,
        event_type: event.type,
        processed_at: new Date().toISOString()
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('💥 Webhook error:', error)
    return new Response(
      JSON.stringify({ 
        error: error.message,
        timestamp: new Date().toISOString()
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})