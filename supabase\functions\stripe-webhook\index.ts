import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import Stripe from 'https://esm.sh/stripe@14.25.0'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
)

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, stripe-signature',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const body = await req.text()
    const signature = req.headers.get('stripe-signature')

    if (!signature) {
      throw new Error('No Stripe signature found')
    }

    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      Deno.env.get('STRIPE_WEBHOOK_SECRET') || ''
    )

    console.log(`Processing webhook event: ${event.type}`)

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session
        
        if (session.mode === 'subscription' && session.subscription) {
          console.log('Processing checkout session completion for subscription')
          
          // Get the subscription details
          const subscription = await stripe.subscriptions.retrieve(session.subscription as string)
          const customer = await stripe.customers.retrieve(session.customer as string) as Stripe.Customer
          
          // Handle subscription creation
          const { error } = await supabaseClient.rpc('handle_subscription_created', {
            stripe_subscription_id: subscription.id,
            stripe_customer_id: customer.id,
            stripe_price_id: subscription.items.data[0].price.id,
            customer_email: customer.email,
            subscription_status: subscription.status,
            current_period_start_unix: subscription.current_period_start,
            current_period_end_unix: subscription.current_period_end
          })
          
          if (error) {
            console.error('Error creating subscription:', error)
            throw error
          }
          
          console.log('Subscription created successfully')
        }
        break
      }

      case 'customer.subscription.created': {
        const subscription = event.data.object as Stripe.Subscription
        const customer = await stripe.customers.retrieve(subscription.customer as string) as Stripe.Customer
        
        console.log('Processing subscription creation')
        
        const { error } = await supabaseClient.rpc('handle_subscription_created', {
          stripe_subscription_id: subscription.id,
          stripe_customer_id: customer.id,
          stripe_price_id: subscription.items.data[0].price.id,
          customer_email: customer.email,
          subscription_status: subscription.status,
          current_period_start_unix: subscription.current_period_start,
          current_period_end_unix: subscription.current_period_end
        })
        
        if (error) {
          console.error('Error creating subscription:', error)
          throw error
        }
        
        console.log('Subscription created successfully')
        break
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        
        console.log('Processing subscription update')
        
        const { error } = await supabaseClient.rpc('handle_subscription_updated', {
          stripe_subscription_id: subscription.id,
          subscription_status: subscription.status,
          current_period_start_unix: subscription.current_period_start,
          current_period_end_unix: subscription.current_period_end,
          cancel_at_period_end: subscription.cancel_at_period_end,
          canceled_at_unix: subscription.canceled_at
        })
        
        if (error) {
          console.error('Error updating subscription:', error)
          throw error
        }
        
        console.log('Subscription updated successfully')
        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        
        console.log('Processing subscription deletion')
        
        const { error } = await supabaseClient.rpc('handle_subscription_deleted', {
          stripe_subscription_id: subscription.id
        })
        
        if (error) {
          console.error('Error deleting subscription:', error)
          throw error
        }
        
        console.log('Subscription deleted successfully')
        break
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        
        if (invoice.subscription) {
          console.log('Processing successful payment')
          
          const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string)
          
          const { error } = await supabaseClient.rpc('handle_subscription_updated', {
            stripe_subscription_id: subscription.id,
            subscription_status: subscription.status,
            current_period_start_unix: subscription.current_period_start,
            current_period_end_unix: subscription.current_period_end,
            cancel_at_period_end: subscription.cancel_at_period_end,
            canceled_at_unix: subscription.canceled_at
          })
          
          if (error) {
            console.error('Error updating subscription after payment:', error)
            throw error
          }
          
          console.log('Subscription updated after successful payment')
        }
        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        
        if (invoice.subscription) {
          console.log('Processing failed payment')
          
          const { error } = await supabaseClient.rpc('handle_subscription_updated', {
            stripe_subscription_id: invoice.subscription as string,
            subscription_status: 'past_due',
            current_period_start_unix: Math.floor(Date.now() / 1000),
            current_period_end_unix: Math.floor(Date.now() / 1000),
            cancel_at_period_end: false,
            canceled_at_unix: null
          })
          
          if (error) {
            console.error('Error updating subscription after failed payment:', error)
            throw error
          }
          
          console.log('Subscription updated after failed payment')
        }
        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return new Response(
      JSON.stringify({ received: true }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Webhook error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})