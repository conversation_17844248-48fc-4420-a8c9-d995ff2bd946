/*
  # Complete Nostoria Database Schema

  1. New Tables
    - `user_profiles` - Extended user profile information
    - `memory_collaborators` - Share memories with other users
    - `comments` - Comments on memories
    - `reactions` - Emoji reactions to memories
    - `shared_links` - Public sharing links for memories
    - `tags` - Global tag system
    - `memory_tags` - Many-to-many relationship between memories and tags

  2. Updates to Existing Tables
    - Add missing columns to existing tables if needed
    - Add proper indexes for performance

  3. Security
    - Enable RLS on all tables
    - Add comprehensive policies for data access
    - Ensure proper user isolation

  4. Functions & Triggers
    - Automatic timestamp updates
    - Auto-create user profiles on signup
*/

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- USER PROFILES TABLE (extends the existing profiles table concept)
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  display_name TEXT NOT NULL,
  avatar_url TEXT,
  bio TEXT DEFAULT '',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add missing columns to existing memories table if they don't exist
DO $$
BEGIN
  -- Add view_count column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'memories' AND column_name = 'view_count'
  ) THEN
    ALTER TABLE memories ADD COLUMN view_count INTEGER DEFAULT 0;
  END IF;

  -- Add memory_type column if it doesn't exist (maps to existing file_type)
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'memories' AND column_name = 'memory_type'
  ) THEN
    ALTER TABLE memories ADD COLUMN memory_type TEXT;
    -- Update memory_type based on existing file_type
    UPDATE memories SET memory_type = 
      CASE 
        WHEN file_type LIKE 'image/%' THEN 'image'
        WHEN file_type LIKE 'video/%' THEN 'video'
        ELSE 'document'
      END;
    -- Add constraint
    ALTER TABLE memories ADD CONSTRAINT memories_memory_type_check 
      CHECK (memory_type IN ('image', 'video', 'document'));
  END IF;

  -- Add visibility column that maps to existing privacy column
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'memories' AND column_name = 'visibility'
  ) THEN
    ALTER TABLE memories ADD COLUMN visibility TEXT;
    -- Map existing privacy values to visibility
    UPDATE memories SET visibility = 
      CASE 
        WHEN privacy = 'private' THEN 'private'
        WHEN privacy = 'public' THEN 'public'
        WHEN privacy = 'family' THEN 'shared'
        ELSE 'private'
      END;
    -- Add constraint
    ALTER TABLE memories ADD CONSTRAINT memories_visibility_check 
      CHECK (visibility IN ('private', 'public', 'shared'));
  END IF;

  -- Add memory_date column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'memories' AND column_name = 'memory_date'
  ) THEN
    ALTER TABLE memories ADD COLUMN memory_date DATE DEFAULT CURRENT_DATE;
  END IF;
END $$;

-- MEMORY COLLABORATORS TABLE
CREATE TABLE IF NOT EXISTS memory_collaborators (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  memory_id UUID REFERENCES memories(id) ON DELETE CASCADE NOT NULL,
  collaborator_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  role TEXT CHECK (role IN ('viewer', 'editor')) DEFAULT 'viewer',
  invited_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(memory_id, collaborator_id)
);

-- COMMENTS TABLE
CREATE TABLE IF NOT EXISTS comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  memory_id UUID REFERENCES memories(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  comment_text TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- REACTIONS TABLE
CREATE TABLE IF NOT EXISTS reactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  memory_id UUID REFERENCES memories(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  emoji TEXT NOT NULL CHECK (char_length(emoji) >= 1),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(memory_id, user_id, emoji)
);

-- SHARED LINKS TABLE
CREATE TABLE IF NOT EXISTS shared_links (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  memory_id UUID REFERENCES memories(id) ON DELETE CASCADE NOT NULL,
  url_slug TEXT UNIQUE NOT NULL,
  expires_at TIMESTAMPTZ,
  view_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- TAGS TABLE (separate from the array-based tags in memories)
CREATE TABLE IF NOT EXISTS tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT UNIQUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- MEMORY TAGS TABLE (Many-to-many relationship)
CREATE TABLE IF NOT EXISTS memory_tags (
  memory_id UUID REFERENCES memories(id) ON DELETE CASCADE NOT NULL,
  tag_id UUID REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (memory_id, tag_id)
);

-- ENABLE ROW LEVEL SECURITY ON NEW TABLES
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_collaborators ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE shared_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_tags ENABLE ROW LEVEL SECURITY;

-- USER PROFILES POLICIES
CREATE POLICY "Users can view all profiles"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can update own profile"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile"
  ON user_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- MEMORY COLLABORATORS POLICIES
CREATE POLICY "Users can view collaborators for their memories"
  ON memory_collaborators
  FOR SELECT
  TO authenticated
  USING (
    memory_id IN (SELECT id FROM memories WHERE user_id = auth.uid())
    OR collaborator_id = auth.uid()
  );

CREATE POLICY "Memory owners can manage collaborators"
  ON memory_collaborators
  FOR ALL
  TO authenticated
  USING (
    memory_id IN (SELECT id FROM memories WHERE user_id = auth.uid())
  );

-- COMMENTS POLICIES
CREATE POLICY "Users can view comments on accessible memories"
  ON comments
  FOR SELECT
  TO authenticated
  USING (
    memory_id IN (
      SELECT id FROM memories 
      WHERE user_id = auth.uid() 
      OR privacy = 'public'
      OR (privacy = 'family' AND id IN (
        SELECT memory_id FROM memory_collaborators 
        WHERE collaborator_id = auth.uid()
      ))
    )
  );

CREATE POLICY "Users can insert comments on accessible memories"
  ON comments
  FOR INSERT
  TO authenticated
  WITH CHECK (
    memory_id IN (
      SELECT id FROM memories 
      WHERE user_id = auth.uid() 
      OR privacy = 'public'
      OR (privacy = 'family' AND id IN (
        SELECT memory_id FROM memory_collaborators 
        WHERE collaborator_id = auth.uid()
      ))
    )
  );

CREATE POLICY "Users can update own comments"
  ON comments
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can delete own comments"
  ON comments
  FOR DELETE
  TO authenticated
  USING (user_id = auth.uid());

-- REACTIONS POLICIES
CREATE POLICY "Users can view reactions on accessible memories"
  ON reactions
  FOR SELECT
  TO authenticated
  USING (
    memory_id IN (
      SELECT id FROM memories 
      WHERE user_id = auth.uid() 
      OR privacy = 'public'
      OR (privacy = 'family' AND id IN (
        SELECT memory_id FROM memory_collaborators 
        WHERE collaborator_id = auth.uid()
      ))
    )
  );

CREATE POLICY "Users can manage own reactions"
  ON reactions
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- SHARED LINKS POLICIES
CREATE POLICY "Memory owners can manage shared links"
  ON shared_links
  FOR ALL
  TO authenticated
  USING (
    memory_id IN (SELECT id FROM memories WHERE user_id = auth.uid())
  );

CREATE POLICY "Anyone can view non-expired shared links"
  ON shared_links
  FOR SELECT
  TO anon, authenticated
  USING (expires_at IS NULL OR expires_at > NOW());

-- TAGS POLICIES
CREATE POLICY "Anyone can view tags"
  ON tags
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can create tags"
  ON tags
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- MEMORY TAGS POLICIES
CREATE POLICY "Users can view memory tags for accessible memories"
  ON memory_tags
  FOR SELECT
  TO authenticated
  USING (
    memory_id IN (
      SELECT id FROM memories 
      WHERE user_id = auth.uid() 
      OR privacy = 'public'
      OR (privacy = 'family' AND id IN (
        SELECT memory_id FROM memory_collaborators 
        WHERE collaborator_id = auth.uid()
      ))
    )
  );

CREATE POLICY "Users can manage tags for own memories"
  ON memory_tags
  FOR ALL
  TO authenticated
  USING (
    memory_id IN (SELECT id FROM memories WHERE user_id = auth.uid())
  );

-- CREATE INDEXES FOR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_memories_user_id ON memories(user_id);
CREATE INDEX IF NOT EXISTS idx_memories_privacy ON memories(privacy);
CREATE INDEX IF NOT EXISTS idx_memories_visibility ON memories(visibility);
CREATE INDEX IF NOT EXISTS idx_memories_created_at ON memories(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_memory_collaborators_memory_id ON memory_collaborators(memory_id);
CREATE INDEX IF NOT EXISTS idx_memory_collaborators_collaborator_id ON memory_collaborators(collaborator_id);
CREATE INDEX IF NOT EXISTS idx_comments_memory_id ON comments(memory_id);
CREATE INDEX IF NOT EXISTS idx_reactions_memory_id ON reactions(memory_id);
CREATE INDEX IF NOT EXISTS idx_shared_links_url_slug ON shared_links(url_slug);
CREATE INDEX IF NOT EXISTS idx_memory_tags_memory_id ON memory_tags(memory_id);
CREATE INDEX IF NOT EXISTS idx_memory_tags_tag_id ON memory_tags(tag_id);

-- CREATE FUNCTIONS FOR AUTOMATIC TIMESTAMPS
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- CREATE TRIGGERS FOR AUTOMATIC TIMESTAMPS
CREATE TRIGGER update_user_profiles_updated_at 
  BEFORE UPDATE ON user_profiles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_comments_updated_at 
  BEFORE UPDATE ON comments 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- CREATE FUNCTION TO AUTOMATICALLY CREATE USER PROFILE
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (id, display_name)
  VALUES (NEW.id, COALESCE(NEW.raw_user_meta_data->>'display_name', split_part(NEW.email, '@', 1)));
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- CREATE TRIGGER FOR NEW USER REGISTRATION (only if it doesn't exist)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'on_auth_user_created'
  ) THEN
    CREATE TRIGGER on_auth_user_created
      AFTER INSERT ON auth.users
      FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
  END IF;
END $$;