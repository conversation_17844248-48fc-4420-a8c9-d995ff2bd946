/*
  # Fix Storage Policies and RLS

  1. Storage Policies
    - Create proper storage policies for the memories bucket
    - Allow authenticated users to upload, view, and delete their own files
    - Allow public access to view files

  2. Database Fixes
    - Ensure all necessary columns exist
    - Fix any constraint issues
    - Add proper indexes
*/

-- Create storage bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('memories', 'memories', true)
ON CONFLICT (id) DO NOTHING;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can upload their own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own files" ON storage.objects;
DROP POLICY IF EXISTS "Public can view files" ON storage.objects;

-- Create storage policies
CREATE POLICY "Users can upload their own files"
  ON storage.objects
  FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'memories' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view their own files"
  ON storage.objects
  FOR SELECT
  TO authenticated
  USING (bucket_id = 'memories' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their own files"
  ON storage.objects
  FOR DELETE
  TO authenticated
  USING (bucket_id = 'memories' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Public can view files"
  ON storage.objects
  FOR SELECT
  TO public
  USING (bucket_id = 'memories');

-- Ensure memories table has all required columns
DO $$
BEGIN
  -- Add description column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'memories' AND column_name = 'description'
  ) THEN
    ALTER TABLE memories ADD COLUMN description TEXT;
  END IF;

  -- Ensure view_count exists and has default
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'memories' AND column_name = 'view_count'
  ) THEN
    ALTER TABLE memories ADD COLUMN view_count INTEGER DEFAULT 0;
  ELSE
    -- Update existing column to have default
    ALTER TABLE memories ALTER COLUMN view_count SET DEFAULT 0;
    -- Update any null values
    UPDATE memories SET view_count = 0 WHERE view_count IS NULL;
  END IF;

  -- Ensure memory_type column exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'memories' AND column_name = 'memory_type'
  ) THEN
    ALTER TABLE memories ADD COLUMN memory_type TEXT;
    -- Update memory_type based on existing file_type
    UPDATE memories SET memory_type = 
      CASE 
        WHEN file_type LIKE 'image/%' THEN 'image'
        WHEN file_type LIKE 'video/%' THEN 'video'
        ELSE 'document'
      END;
  END IF;

  -- Ensure visibility column exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'memories' AND column_name = 'visibility'
  ) THEN
    ALTER TABLE memories ADD COLUMN visibility TEXT DEFAULT 'private';
    -- Map existing privacy values to visibility
    UPDATE memories SET visibility = 
      CASE 
        WHEN privacy = 'private' THEN 'private'
        WHEN privacy = 'public' THEN 'public'
        WHEN privacy = 'family' THEN 'shared'
        ELSE 'private'
      END;
  END IF;

  -- Ensure memory_date column exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'memories' AND column_name = 'memory_date'
  ) THEN
    ALTER TABLE memories ADD COLUMN memory_date DATE DEFAULT CURRENT_DATE;
  END IF;
END $$;

-- Add constraints if they don't exist
DO $$
BEGIN
  -- Add memory_type constraint
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'memories_memory_type_check'
  ) THEN
    ALTER TABLE memories ADD CONSTRAINT memories_memory_type_check 
      CHECK (memory_type IN ('image', 'video', 'document'));
  END IF;

  -- Add visibility constraint
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'memories_visibility_check'
  ) THEN
    ALTER TABLE memories ADD CONSTRAINT memories_visibility_check 
      CHECK (visibility IN ('private', 'public', 'shared'));
  END IF;
END $$;

-- Update existing memories policies to work with new columns
DROP POLICY IF EXISTS "Users can view their own memories" ON memories;
DROP POLICY IF EXISTS "Users can insert their own memories" ON memories;
DROP POLICY IF EXISTS "Users can update their own memories" ON memories;
DROP POLICY IF EXISTS "Users can delete their own memories" ON memories;

-- Recreate memory policies
CREATE POLICY "Users can view their own memories"
  ON memories
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid() OR visibility = 'public');

CREATE POLICY "Users can insert their own memories"
  ON memories
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own memories"
  ON memories
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own memories"
  ON memories
  FOR DELETE
  TO authenticated
  USING (user_id = auth.uid());

-- Create function to handle new user profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (id, display_name)
  VALUES (NEW.id, COALESCE(NEW.raw_user_meta_data->>'display_name', split_part(NEW.email, '@', 1)))
  ON CONFLICT (id) DO NOTHING;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();