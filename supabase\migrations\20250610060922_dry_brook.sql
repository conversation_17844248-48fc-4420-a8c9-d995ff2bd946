/*
  # Complete RLS Recursion Fix

  1. Problem Analysis
    - The memories table SELECT policy references memory_collaborators
    - The memory_collaborators policies reference memories
    - This creates infinite recursion during policy evaluation

  2. Solution
    - Simplify all policies to avoid cross-table references
    - Use direct user_id checks where possible
    - Create separate policies for different access patterns
    - Remove circular dependencies completely

  3. Security
    - Maintain proper access control
    - Ensure users can only access their own data
    - Allow public access where appropriate
*/

-- First, drop all existing policies that might cause recursion
DROP POLICY IF EXISTS "Users can view accessible memories" ON memories;
DROP POLICY IF EXISTS "Users can insert their own memories" ON memories;
DROP POLICY IF EXISTS "Users can update their own memories" ON memories;
DROP POLICY IF EXISTS "Users can delete their own memories" ON memories;

DROP POLICY IF EXISTS "Users can view collaborators for their memories" ON memory_collaborators;
DROP POLICY IF EXISTS "Memory owners can manage collaborators" ON memory_collaborators;

DROP POLICY IF EXISTS "Users can view comments on accessible memories" ON comments;
DROP POLICY IF EXISTS "Users can insert comments on accessible memories" ON comments;
DROP POLICY IF EXISTS "Users can update own comments" ON comments;
DROP POLICY IF EXISTS "Users can delete own comments" ON comments;

DROP POLICY IF EXISTS "Users can view reactions on accessible memories" ON reactions;
DROP POLICY IF EXISTS "Users can manage own reactions" ON reactions;

DROP POLICY IF EXISTS "Users can view memory tags for accessible memories" ON memory_tags;
DROP POLICY IF EXISTS "Users can manage tags for own memories" ON memory_tags;

-- Drop the problematic function if it exists
DROP FUNCTION IF EXISTS get_memory_owner(UUID);

-- Create simple, non-recursive policies for memories table
CREATE POLICY "Users can view own memories"
  ON memories
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can view public memories"
  ON memories
  FOR SELECT
  TO authenticated
  USING (visibility = 'public');

CREATE POLICY "Users can insert own memories"
  ON memories
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own memories"
  ON memories
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can delete own memories"
  ON memories
  FOR DELETE
  TO authenticated
  USING (user_id = auth.uid());

-- Simple policies for memory_collaborators (no cross-references)
CREATE POLICY "Users can view own collaborations"
  ON memory_collaborators
  FOR SELECT
  TO authenticated
  USING (collaborator_id = auth.uid());

CREATE POLICY "Users can manage collaborators for own memories"
  ON memory_collaborators
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM memories 
      WHERE memories.id = memory_collaborators.memory_id 
      AND memories.user_id = auth.uid()
    )
  );

-- Simple policies for comments (direct memory ownership check)
CREATE POLICY "Users can view comments on own memories"
  ON comments
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM memories 
      WHERE memories.id = comments.memory_id 
      AND memories.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view comments on public memories"
  ON comments
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM memories 
      WHERE memories.id = comments.memory_id 
      AND memories.visibility = 'public'
    )
  );

CREATE POLICY "Users can insert comments on own memories"
  ON comments
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM memories 
      WHERE memories.id = comments.memory_id 
      AND memories.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert comments on public memories"
  ON comments
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM memories 
      WHERE memories.id = comments.memory_id 
      AND memories.visibility = 'public'
    )
  );

CREATE POLICY "Users can update own comments"
  ON comments
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can delete own comments"
  ON comments
  FOR DELETE
  TO authenticated
  USING (user_id = auth.uid());

-- Simple policies for reactions
CREATE POLICY "Users can view reactions on own memories"
  ON reactions
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM memories 
      WHERE memories.id = reactions.memory_id 
      AND memories.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view reactions on public memories"
  ON reactions
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM memories 
      WHERE memories.id = reactions.memory_id 
      AND memories.visibility = 'public'
    )
  );

CREATE POLICY "Users can manage own reactions"
  ON reactions
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Simple policies for memory_tags
CREATE POLICY "Users can view tags on own memories"
  ON memory_tags
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM memories 
      WHERE memories.id = memory_tags.memory_id 
      AND memories.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view tags on public memories"
  ON memory_tags
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM memories 
      WHERE memories.id = memory_tags.memory_id 
      AND memories.visibility = 'public'
    )
  );

CREATE POLICY "Users can manage tags on own memories"
  ON memory_tags
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM memories 
      WHERE memories.id = memory_tags.memory_id 
      AND memories.user_id = auth.uid()
    )
  );

-- Ensure shared_links policies are simple
DROP POLICY IF EXISTS "Memory owners can manage shared links" ON shared_links;
DROP POLICY IF EXISTS "Anyone can view non-expired shared links" ON shared_links;

CREATE POLICY "Users can manage shared links for own memories"
  ON shared_links
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM memories 
      WHERE memories.id = shared_links.memory_id 
      AND memories.user_id = auth.uid()
    )
  );

CREATE POLICY "Anyone can view non-expired shared links"
  ON shared_links
  FOR SELECT
  TO anon, authenticated
  USING (expires_at IS NULL OR expires_at > NOW());

-- Ensure user_profiles policies are simple
DROP POLICY IF EXISTS "Users can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;

CREATE POLICY "Users can view all profiles"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can insert own profile"
  ON user_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Ensure tags policies are simple
DROP POLICY IF EXISTS "Anyone can view tags" ON tags;
DROP POLICY IF EXISTS "Users can create tags" ON tags;

CREATE POLICY "Anyone can view tags"
  ON tags
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can create tags"
  ON tags
  FOR INSERT
  TO authenticated
  WITH CHECK (true);