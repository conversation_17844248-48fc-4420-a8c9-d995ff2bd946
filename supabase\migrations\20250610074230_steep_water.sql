/*
  # Fix Database Relationships and Upload Issues

  1. Problem Analysis
    - No direct foreign key between memories and user_profiles
    - Both reference auth.users but PostgREST can't infer the relationship
    - Need to fix the join queries to work with the actual schema

  2. Solution
    - Keep the existing schema structure (it's correct)
    - Fix the application queries to not rely on automatic joins
    - Ensure all policies work correctly
    - Add proper indexes for performance

  3. Storage
    - Ensure storage bucket exists with proper policies
    - Fix upload permissions
*/

-- Ensure the storage bucket exists with proper configuration
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'memories', 
  'memories', 
  true, 
  52428800, -- 50MB limit
  ARRAY[
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
    'video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm', 'video/avi',
    'application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 
    'application/msword', 'text/plain', 'text/csv'
  ]
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Clean up ALL existing storage policies to avoid conflicts
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT policyname FROM pg_policies WHERE tablename = 'objects' AND schemaname = 'storage')
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON storage.objects';
    END LOOP;
END $$;

-- Create comprehensive storage policies
CREATE POLICY "Anyone can view files in memories bucket"
  ON storage.objects
  FOR SELECT
  TO public
  USING (bucket_id = 'memories');

CREATE POLICY "Authenticated users can upload to memories bucket"
  ON storage.objects
  FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'memories');

CREATE POLICY "Authenticated users can update files in memories bucket"
  ON storage.objects
  FOR UPDATE
  TO authenticated
  USING (bucket_id = 'memories');

CREATE POLICY "Users can delete their own files"
  ON storage.objects
  FOR DELETE
  TO authenticated
  USING (
    bucket_id = 'memories' 
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

-- Ensure memories table has the correct structure
DO $$
BEGIN
    -- Create memories table if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'memories' AND table_schema = 'public') THEN
        CREATE TABLE memories (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            title TEXT NOT NULL,
            description TEXT,
            memory_type TEXT,
            file_url TEXT,
            thumbnail_url TEXT,
            tags TEXT[] DEFAULT '{}',
            visibility TEXT DEFAULT 'private',
            memory_date DATE DEFAULT CURRENT_DATE,
            view_count INTEGER DEFAULT 0,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
    ELSE
        -- Ensure all required columns exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'description') THEN
            ALTER TABLE memories ADD COLUMN description TEXT;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'memory_type') THEN
            ALTER TABLE memories ADD COLUMN memory_type TEXT;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'file_url') THEN
            ALTER TABLE memories ADD COLUMN file_url TEXT;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'thumbnail_url') THEN
            ALTER TABLE memories ADD COLUMN thumbnail_url TEXT;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'tags') THEN
            ALTER TABLE memories ADD COLUMN tags TEXT[] DEFAULT '{}';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'visibility') THEN
            ALTER TABLE memories ADD COLUMN visibility TEXT DEFAULT 'private';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'memory_date') THEN
            ALTER TABLE memories ADD COLUMN memory_date DATE DEFAULT CURRENT_DATE;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'view_count') THEN
            ALTER TABLE memories ADD COLUMN view_count INTEGER DEFAULT 0;
        END IF;
    END IF;
END $$;

-- Ensure user_profiles table exists
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    display_name TEXT NOT NULL,
    avatar_url TEXT,
    bio TEXT DEFAULT '',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Clean up existing database policies to avoid conflicts
DO $$
DECLARE
    r RECORD;
BEGIN
    -- Drop all policies on memories table
    FOR r IN (SELECT policyname FROM pg_policies WHERE tablename = 'memories' AND schemaname = 'public')
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON memories';
    END LOOP;
    
    -- Drop all policies on user_profiles table
    FOR r IN (SELECT policyname FROM pg_policies WHERE tablename = 'user_profiles' AND schemaname = 'public')
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON user_profiles';
    END LOOP;
END $$;

-- Drop and recreate constraints to avoid conflicts
DO $$
BEGIN
    -- Drop existing constraints if they exist
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'memories_memory_type_check' AND table_name = 'memories') THEN
        ALTER TABLE memories DROP CONSTRAINT memories_memory_type_check;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'memories_visibility_check' AND table_name = 'memories') THEN
        ALTER TABLE memories DROP CONSTRAINT memories_visibility_check;
    END IF;
END $$;

-- Add constraints
ALTER TABLE memories ADD CONSTRAINT memories_memory_type_check 
    CHECK (memory_type IN ('image', 'video', 'document'));

ALTER TABLE memories ADD CONSTRAINT memories_visibility_check 
    CHECK (visibility IN ('private', 'public', 'shared'));

-- Enable RLS
ALTER TABLE memories ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create simple, working policies for memories
CREATE POLICY "authenticated_users_select_own_memories"
    ON memories
    FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "authenticated_users_select_public_memories"
    ON memories
    FOR SELECT
    TO authenticated
    USING (visibility = 'public');

CREATE POLICY "authenticated_users_insert_own_memories"
    ON memories
    FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "authenticated_users_update_own_memories"
    ON memories
    FOR UPDATE
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "authenticated_users_delete_own_memories"
    ON memories
    FOR DELETE
    TO authenticated
    USING (user_id = auth.uid());

-- Create policies for user_profiles
CREATE POLICY "authenticated_users_view_all_profiles"
    ON user_profiles
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "authenticated_users_insert_own_profile"
    ON user_profiles
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = id);

CREATE POLICY "authenticated_users_update_own_profile"
    ON user_profiles
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = id);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_memories_user_id ON memories(user_id);
CREATE INDEX IF NOT EXISTS idx_memories_visibility ON memories(visibility);
CREATE INDEX IF NOT EXISTS idx_memories_created_at ON memories(created_at DESC);

-- Function to handle new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, display_name)
    VALUES (
        NEW.id, 
        COALESCE(
            NEW.raw_user_meta_data->>'display_name',
            NEW.raw_user_meta_data->>'full_name', 
            split_part(NEW.email, '@', 1)
        )
    )
    ON CONFLICT (id) DO NOTHING;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;