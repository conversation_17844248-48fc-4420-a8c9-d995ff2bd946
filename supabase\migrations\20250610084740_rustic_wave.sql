/*
  # Add Subscription System

  1. New Tables
    - `subscriptions` - User subscription records
    - `subscription_plans` - Available subscription plans

  2. Security
    - Enable RLS on new tables
    - Add policies for user access

  3. Functions
    - Function to check if user can upload more memories
    - Function to get user's memory count
*/

-- Create subscription plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  price_monthly INTEGER, -- Price in pence
  price_yearly INTEGER,  -- Price in pence
  stripe_price_id_monthly TEXT,
  stripe_price_id_yearly TEXT,
  memory_limit INTEGER, -- NULL means unlimited
  features TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_id TEXT NOT NULL REFERENCES subscription_plans(id),
  stripe_subscription_id TEXT UNIQUE,
  stripe_customer_id TEXT,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'canceled', 'past_due', 'incomplete')),
  billing_cycle TEXT NOT NULL DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'yearly')),
  current_period_start TIMESTAMPTZ,
  current_period_end TIMESTAMPTZ,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Policies for subscription_plans (public read)
CREATE POLICY "Anyone can view subscription plans"
  ON subscription_plans
  FOR SELECT
  TO public
  USING (true);

-- Policies for subscriptions
CREATE POLICY "Users can view own subscriptions"
  ON subscriptions
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can insert own subscriptions"
  ON subscriptions
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own subscriptions"
  ON subscriptions
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

-- Insert default subscription plans
INSERT INTO subscription_plans (id, name, description, price_monthly, price_yearly, memory_limit, features) VALUES
('free', 'Free', 'Perfect for getting started', 0, 0, 50, ARRAY['50 memories', 'Basic sharing', 'Standard support']),
('unlimited', 'Unlimited', 'For memory collectors', 1000, 12000, NULL, ARRAY['Unlimited memories', 'Advanced sharing', 'Priority support', 'Early access to features'])
ON CONFLICT (id) DO NOTHING;

-- Function to get user's current memory count
CREATE OR REPLACE FUNCTION get_user_memory_count(user_uuid UUID)
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::INTEGER 
    FROM memories 
    WHERE user_id = user_uuid
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can upload more memories
CREATE OR REPLACE FUNCTION can_user_upload_memory(user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
  memory_count INTEGER;
  user_plan_limit INTEGER;
BEGIN
  -- Get user's current memory count
  memory_count := get_user_memory_count(user_uuid);
  
  -- Get user's plan limit
  SELECT sp.memory_limit INTO user_plan_limit
  FROM subscriptions s
  JOIN subscription_plans sp ON s.plan_id = sp.id
  WHERE s.user_id = user_uuid 
    AND s.status = 'active'
    AND (s.current_period_end IS NULL OR s.current_period_end > NOW())
  ORDER BY s.created_at DESC
  LIMIT 1;
  
  -- If no active subscription, use free plan limit
  IF user_plan_limit IS NULL THEN
    user_plan_limit := 50; -- Free plan limit
  END IF;
  
  -- NULL limit means unlimited
  IF user_plan_limit IS NULL THEN
    RETURN TRUE;
  END IF;
  
  -- Check if under limit
  RETURN memory_count < user_plan_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's subscription info
CREATE OR REPLACE FUNCTION get_user_subscription_info(user_uuid UUID)
RETURNS TABLE (
  plan_id TEXT,
  plan_name TEXT,
  memory_limit INTEGER,
  memory_count INTEGER,
  can_upload BOOLEAN,
  status TEXT,
  current_period_end TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(s.plan_id, 'free') as plan_id,
    COALESCE(sp.name, 'Free') as plan_name,
    COALESCE(sp.memory_limit, 50) as memory_limit,
    get_user_memory_count(user_uuid) as memory_count,
    can_user_upload_memory(user_uuid) as can_upload,
    COALESCE(s.status, 'active') as status,
    s.current_period_end
  FROM (SELECT user_uuid as uid) u
  LEFT JOIN subscriptions s ON s.user_id = u.uid 
    AND s.status = 'active'
    AND (s.current_period_end IS NULL OR s.current_period_end > NOW())
  LEFT JOIN subscription_plans sp ON s.plan_id = sp.id
  ORDER BY s.created_at DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_user_memory_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION can_user_upload_memory(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_subscription_info(UUID) TO authenticated;

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_subscription_id ON subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);

-- Add trigger for updated_at
CREATE TRIGGER update_subscriptions_updated_at 
  BEFORE UPDATE ON subscriptions 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();