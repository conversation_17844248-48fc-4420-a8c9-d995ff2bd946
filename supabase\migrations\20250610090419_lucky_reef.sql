/*
  # Stripe Payment Links System

  1. Simplified subscription system using Stripe payment links
  2. Manual subscription management
  3. Simple database structure for tracking subscriptions
*/

-- Update subscription plans with payment link URLs instead of price IDs
UPDATE subscription_plans 
SET 
  stripe_price_id_monthly = 'https://buy.stripe.com/your-monthly-link',
  stripe_price_id_yearly = 'https://buy.stripe.com/your-yearly-link'
WHERE id = 'unlimited';

-- Add a simple subscription tracking table for manual management
CREATE TABLE IF NOT EXISTS manual_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_id TEXT NOT NULL DEFAULT 'unlimited',
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'canceled')),
  billing_cycle TEXT NOT NULL CHECK (billing_cycle IN ('monthly', 'yearly')),
  start_date DATE NOT NULL DEFAULT CURRENT_DATE,
  end_date DATE,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE manual_subscriptions ENABLE ROW LEVEL SECURITY;

-- Policies for manual subscriptions
CREATE POLICY "Users can view own manual subscriptions"
  ON manual_subscriptions
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

-- Admin function to manually add subscriptions (you'll use this from Supabase dashboard)
CREATE OR REPLACE FUNCTION add_manual_subscription(
  user_email TEXT,
  plan_type TEXT DEFAULT 'unlimited',
  billing_type TEXT DEFAULT 'yearly',
  duration_months INTEGER DEFAULT 12
)
RETURNS UUID AS $$
DECLARE
  target_user_id UUID;
  subscription_id UUID;
  end_date_calc DATE;
BEGIN
  -- Find user by email
  SELECT id INTO target_user_id
  FROM auth.users
  WHERE email = user_email;
  
  IF target_user_id IS NULL THEN
    RAISE EXCEPTION 'User with email % not found', user_email;
  END IF;
  
  -- Calculate end date
  end_date_calc := CURRENT_DATE + (duration_months || ' months')::INTERVAL;
  
  -- Insert subscription
  INSERT INTO manual_subscriptions (
    user_id, 
    plan_id, 
    billing_cycle, 
    end_date,
    notes
  ) VALUES (
    target_user_id,
    plan_type,
    billing_type,
    end_date_calc,
    'Added manually via payment link'
  ) RETURNING id INTO subscription_id;
  
  RETURN subscription_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Updated function to get user subscription info (includes manual subscriptions)
CREATE OR REPLACE FUNCTION get_user_subscription_info(user_uuid UUID)
RETURNS TABLE (
  plan_id TEXT,
  plan_name TEXT,
  memory_limit INTEGER,
  memory_count INTEGER,
  can_upload BOOLEAN,
  status TEXT,
  current_period_end TIMESTAMPTZ
) AS $$
DECLARE
  active_subscription RECORD;
  user_memory_count INTEGER;
  plan_limit INTEGER;
BEGIN
  -- Get user's memory count
  SELECT COUNT(*)::INTEGER INTO user_memory_count
  FROM memories 
  WHERE user_id = user_uuid;
  
  -- Check for active manual subscription first
  SELECT * INTO active_subscription
  FROM manual_subscriptions ms
  WHERE ms.user_id = user_uuid 
    AND ms.status = 'active'
    AND (ms.end_date IS NULL OR ms.end_date > CURRENT_DATE)
  ORDER BY ms.created_at DESC
  LIMIT 1;
  
  -- If no manual subscription, check regular subscriptions
  IF active_subscription IS NULL THEN
    SELECT 
      s.plan_id,
      s.status,
      s.current_period_end
    INTO active_subscription
    FROM subscriptions s
    WHERE s.user_id = user_uuid 
      AND s.status = 'active'
      AND (s.current_period_end IS NULL OR s.current_period_end > NOW())
    ORDER BY s.created_at DESC
    LIMIT 1;
  END IF;
  
  -- Determine plan details
  IF active_subscription IS NOT NULL AND active_subscription.plan_id = 'unlimited' THEN
    plan_limit := NULL; -- Unlimited
    RETURN QUERY SELECT 
      'unlimited'::TEXT,
      'Unlimited'::TEXT,
      plan_limit,
      user_memory_count,
      TRUE, -- Can always upload
      COALESCE(active_subscription.status, 'active')::TEXT,
      active_subscription.current_period_end;
  ELSE
    -- Free plan
    plan_limit := 50;
    RETURN QUERY SELECT 
      'free'::TEXT,
      'Free'::TEXT,
      plan_limit,
      user_memory_count,
      (user_memory_count < plan_limit), -- Can upload if under limit
      'active'::TEXT,
      NULL::TIMESTAMPTZ;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION add_manual_subscription(TEXT, TEXT, TEXT, INTEGER) TO authenticated;
GRANT ALL ON manual_subscriptions TO authenticated;

-- Create index
CREATE INDEX IF NOT EXISTS idx_manual_subscriptions_user_id ON manual_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_manual_subscriptions_status ON manual_subscriptions(status);