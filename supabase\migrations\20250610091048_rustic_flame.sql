/*
  # Automated Stripe Integration

  1. Enhanced subscription system
  2. Automatic webhook processing
  3. Real-time subscription updates
  4. Proper Stripe price IDs
*/

-- Update subscription plans with real Stripe price IDs
UPDATE subscription_plans 
SET 
  stripe_price_id_monthly = 'price_1234567890abcdef', -- You'll replace this with your actual Stripe price ID
  stripe_price_id_yearly = 'price_0987654321fedcba'   -- You'll replace this with your actual Stripe price ID
WHERE id = 'unlimited';

-- Enhanced subscriptions table for full automation
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS stripe_price_id TEXT;
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS trial_end TIMESTAMPTZ;
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS canceled_at TIMESTAMPTZ;

-- Function to automatically handle subscription creation from webhook
CREATE OR REPLACE FUNCTION handle_subscription_created(
  stripe_subscription_id TEXT,
  stripe_customer_id TEXT,
  stripe_price_id TEXT,
  customer_email TEXT,
  subscription_status TEXT,
  current_period_start_unix BIGINT,
  current_period_end_unix BIGINT
)
RETURNS UUID AS $$
DECLARE
  target_user_id UUID;
  subscription_id UUID;
  billing_cycle TEXT;
  plan_type TEXT;
BEGIN
  -- Find user by email
  SELECT id INTO target_user_id
  FROM auth.users
  WHERE email = customer_email;
  
  IF target_user_id IS NULL THEN
    RAISE EXCEPTION 'User with email % not found', customer_email;
  END IF;
  
  -- Determine plan and billing cycle from price ID
  SELECT 
    CASE 
      WHEN stripe_price_id_monthly = stripe_price_id THEN 'monthly'
      WHEN stripe_price_id_yearly = stripe_price_id THEN 'yearly'
      ELSE 'monthly'
    END,
    id
  INTO billing_cycle, plan_type
  FROM subscription_plans
  WHERE stripe_price_id_monthly = stripe_price_id 
     OR stripe_price_id_yearly = stripe_price_id
  LIMIT 1;
  
  -- Default to unlimited plan if not found
  IF plan_type IS NULL THEN
    plan_type := 'unlimited';
    billing_cycle := 'monthly';
  END IF;
  
  -- Insert or update subscription
  INSERT INTO subscriptions (
    user_id,
    plan_id,
    stripe_subscription_id,
    stripe_customer_id,
    stripe_price_id,
    status,
    billing_cycle,
    current_period_start,
    current_period_end
  ) VALUES (
    target_user_id,
    plan_type,
    stripe_subscription_id,
    stripe_customer_id,
    stripe_price_id,
    subscription_status,
    billing_cycle,
    to_timestamp(current_period_start_unix),
    to_timestamp(current_period_end_unix)
  )
  ON CONFLICT (stripe_subscription_id) 
  DO UPDATE SET
    status = EXCLUDED.status,
    current_period_start = EXCLUDED.current_period_start,
    current_period_end = EXCLUDED.current_period_end,
    updated_at = NOW()
  RETURNING id INTO subscription_id;
  
  RETURN subscription_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle subscription updates from webhook
CREATE OR REPLACE FUNCTION handle_subscription_updated(
  stripe_subscription_id TEXT,
  subscription_status TEXT,
  current_period_start_unix BIGINT,
  current_period_end_unix BIGINT,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  canceled_at_unix BIGINT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE subscriptions 
  SET 
    status = subscription_status,
    current_period_start = to_timestamp(current_period_start_unix),
    current_period_end = to_timestamp(current_period_end_unix),
    cancel_at_period_end = handle_subscription_updated.cancel_at_period_end,
    canceled_at = CASE 
      WHEN canceled_at_unix IS NOT NULL THEN to_timestamp(canceled_at_unix)
      ELSE NULL 
    END,
    updated_at = NOW()
  WHERE subscriptions.stripe_subscription_id = handle_subscription_updated.stripe_subscription_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle subscription deletion from webhook
CREATE OR REPLACE FUNCTION handle_subscription_deleted(
  stripe_subscription_id TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE subscriptions 
  SET 
    status = 'canceled',
    canceled_at = NOW(),
    updated_at = NOW()
  WHERE subscriptions.stripe_subscription_id = handle_subscription_deleted.stripe_subscription_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions for webhook functions
GRANT EXECUTE ON FUNCTION handle_subscription_created(TEXT, TEXT, TEXT, TEXT, TEXT, BIGINT, BIGINT) TO service_role;
GRANT EXECUTE ON FUNCTION handle_subscription_updated(TEXT, TEXT, BIGINT, BIGINT, BOOLEAN, BIGINT) TO service_role;
GRANT EXECUTE ON FUNCTION handle_subscription_deleted(TEXT) TO service_role;

-- Create indexes for webhook performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer_id ON subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_price_id ON subscriptions(stripe_price_id);