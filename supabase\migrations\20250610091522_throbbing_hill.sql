/*
  # Automated Payment Links Solution

  1. Updates subscription plans with Payment Link URLs
  2. Creates webhook handlers for automatic subscription management
  3. No manual processing required - fully automated
*/

-- Update subscription plans to use Payment Links instead of price IDs
UPDATE subscription_plans 
SET 
  stripe_price_id_monthly = 'https://buy.stripe.com/test_monthly_link', -- Replace with your actual monthly payment link
  stripe_price_id_yearly = 'https://buy.stripe.com/test_yearly_link',   -- Replace with your actual yearly payment link
  description = 'Unlimited memories with automatic activation'
WHERE id = 'unlimited';

-- Enhanced webhook handling for payment links
CREATE OR REPLACE FUNCTION handle_payment_link_success(
  customer_email TEXT,
  payment_intent_id TEXT,
  amount_received INTEGER,
  currency TEXT DEFAULT 'gbp'
)
RETURNS UUID AS $$
DECLARE
  target_user_id UUID;
  subscription_id UUID;
  billing_cycle TEXT;
  plan_type TEXT := 'unlimited';
  end_date DATE;
BEGIN
  -- Find user by email
  SELECT id INTO target_user_id
  FROM auth.users
  WHERE email = customer_email;
  
  IF target_user_id IS NULL THEN
    RAISE EXCEPTION 'User with email % not found', customer_email;
  END IF;
  
  -- Determine billing cycle based on amount
  -- £10 = monthly, £120 = yearly (amounts in pence)
  IF amount_received >= 12000 THEN
    billing_cycle := 'yearly';
    end_date := CURRENT_DATE + INTERVAL '1 year';
  ELSE
    billing_cycle := 'monthly';
    end_date := CURRENT_DATE + INTERVAL '1 month';
  END IF;
  
  -- Deactivate any existing subscriptions
  UPDATE subscriptions 
  SET status = 'canceled', updated_at = NOW()
  WHERE user_id = target_user_id AND status = 'active';
  
  UPDATE manual_subscriptions 
  SET status = 'canceled', updated_at = NOW()
  WHERE user_id = target_user_id AND status = 'active';
  
  -- Create new subscription record
  INSERT INTO subscriptions (
    user_id,
    plan_id,
    stripe_subscription_id,
    stripe_customer_id,
    status,
    billing_cycle,
    current_period_start,
    current_period_end
  ) VALUES (
    target_user_id,
    plan_type,
    payment_intent_id, -- Use payment intent as subscription ID for payment links
    'payment_link_customer',
    'active',
    billing_cycle,
    NOW(),
    end_date::TIMESTAMPTZ
  ) RETURNING id INTO subscription_id;
  
  RETURN subscription_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle checkout session completion (works with payment links)
CREATE OR REPLACE FUNCTION handle_checkout_completed(
  checkout_session_id TEXT,
  customer_email TEXT,
  payment_intent_id TEXT,
  amount_total INTEGER,
  currency TEXT DEFAULT 'gbp'
)
RETURNS UUID AS $$
BEGIN
  RETURN handle_payment_link_success(
    customer_email,
    payment_intent_id,
    amount_total,
    currency
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION handle_payment_link_success(TEXT, TEXT, INTEGER, TEXT) TO service_role;
GRANT EXECUTE ON FUNCTION handle_checkout_completed(TEXT, TEXT, TEXT, INTEGER, TEXT) TO service_role;