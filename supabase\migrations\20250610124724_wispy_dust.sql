/*
  # Fix Database Structure and Policies

  1. Problem Analysis
    - Policies already exist and need to be properly dropped
    - Need to handle existing table structures safely
    - Ensure all policies are recreated correctly

  2. Solution
    - Drop all existing policies systematically
    - Recreate tables only if they don't exist
    - Add missing columns safely
    - Recreate all policies with unique names
*/

-- Drop ALL existing policies on memories table
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT policyname FROM pg_policies WHERE tablename = 'memories' AND schemaname = 'public')
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON memories';
    END LOOP;
END $$;

-- Drop ALL existing policies on user_profiles table
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT policyname FROM pg_policies WHERE tablename = 'user_profiles' AND schemaname = 'public')
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON user_profiles';
    END LOOP;
END $$;

-- Ensure memories table exists with correct structure
DO $$
BEGIN
    -- Create table if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'memories' AND table_schema = 'public') THEN
        CREATE TABLE memories (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            title TEXT NOT NULL,
            description TEXT,
            memory_type TEXT,
            file_url TEXT,
            thumbnail_url TEXT,
            tags TEXT[] DEFAULT '{}',
            visibility TEXT DEFAULT 'private',
            memory_date DATE DEFAULT CURRENT_DATE,
            view_count INTEGER DEFAULT 0,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
    ELSE
        -- Add missing columns if they don't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'description') THEN
            ALTER TABLE memories ADD COLUMN description TEXT;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'memory_type') THEN
            ALTER TABLE memories ADD COLUMN memory_type TEXT;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'file_url') THEN
            ALTER TABLE memories ADD COLUMN file_url TEXT;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'thumbnail_url') THEN
            ALTER TABLE memories ADD COLUMN thumbnail_url TEXT;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'tags') THEN
            ALTER TABLE memories ADD COLUMN tags TEXT[] DEFAULT '{}';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'visibility') THEN
            ALTER TABLE memories ADD COLUMN visibility TEXT DEFAULT 'private';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'memory_date') THEN
            ALTER TABLE memories ADD COLUMN memory_date DATE DEFAULT CURRENT_DATE;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'view_count') THEN
            ALTER TABLE memories ADD COLUMN view_count INTEGER DEFAULT 0;
        END IF;
    END IF;
END $$;

-- Ensure user_profiles table exists
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    display_name TEXT NOT NULL,
    avatar_url TEXT,
    bio TEXT DEFAULT '',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Drop and recreate constraints to avoid conflicts
DO $$
BEGIN
    -- Drop existing constraints if they exist
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'memories_memory_type_check' AND table_name = 'memories') THEN
        ALTER TABLE memories DROP CONSTRAINT memories_memory_type_check;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'memories_visibility_check' AND table_name = 'memories') THEN
        ALTER TABLE memories DROP CONSTRAINT memories_visibility_check;
    END IF;
END $$;

-- Add constraints
ALTER TABLE memories ADD CONSTRAINT memories_memory_type_check 
    CHECK (memory_type IN ('image', 'video', 'document'));

ALTER TABLE memories ADD CONSTRAINT memories_visibility_check 
    CHECK (visibility IN ('private', 'public', 'shared'));

-- Enable RLS
ALTER TABLE memories ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create new policies with unique names for memories
CREATE POLICY "memories_select_own"
    ON memories
    FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "memories_select_public"
    ON memories
    FOR SELECT
    TO authenticated
    USING (visibility = 'public');

CREATE POLICY "memories_insert_own"
    ON memories
    FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "memories_update_own"
    ON memories
    FOR UPDATE
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "memories_delete_own"
    ON memories
    FOR DELETE
    TO authenticated
    USING (user_id = auth.uid());

-- Create new policies with unique names for user_profiles
CREATE POLICY "profiles_select_all"
    ON user_profiles
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "profiles_insert_own"
    ON user_profiles
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own"
    ON user_profiles
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = id);

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_memories_user_id ON memories(user_id);
CREATE INDEX IF NOT EXISTS idx_memories_visibility ON memories(visibility);
CREATE INDEX IF NOT EXISTS idx_memories_created_at ON memories(created_at DESC);

-- Function to handle new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, display_name)
    VALUES (
        NEW.id, 
        COALESCE(
            NEW.raw_user_meta_data->>'display_name',
            NEW.raw_user_meta_data->>'full_name', 
            split_part(NEW.email, '@', 1)
        )
    )
    ON CONFLICT (id) DO NOTHING;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();