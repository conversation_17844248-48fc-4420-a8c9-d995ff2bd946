/*
  # Initial Nostoria Database Schema

  1. New Tables
    - `user_profiles` - User profile information
    - `memories` - Core memory storage
    - `subscription_plans` - Available subscription plans
    - `subscriptions` - User subscriptions
    - `manual_subscriptions` - Manual subscription overrides
    - `comments` - Memory comments
    - `reactions` - Memory reactions
    - `shared_links` - Public memory sharing
    - `tags` - Tag system
    - `memory_tags` - Memory-tag relationships
    - `memory_collaborators` - Memory sharing with specific users
    - `families` - Family groups
    - `user_families` - User-family relationships
    - `legacy_letters` - Future letter delivery system

  2. Security
    - Enable RLS on all tables
    - Add appropriate policies for data access
    - Set up user authentication triggers

  3. Functions
    - Auto-create user profiles on signup
    - Handle subscription management
    - View count increment function
*/

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create user profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  display_name text NOT NULL,
  avatar_url text,
  bio text DEFAULT '',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create subscription plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
  id text PRIMARY KEY,
  name text NOT NULL,
  description text,
  price_monthly integer,
  price_yearly integer,
  stripe_price_id_monthly text,
  stripe_price_id_yearly text,
  memory_limit integer,
  features text[] DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

-- Create memories table
CREATE TABLE IF NOT EXISTS memories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title text NOT NULL,
  description text,
  memory_type text CHECK (memory_type IN ('image', 'video', 'document')),
  file_url text,
  thumbnail_url text,
  tags text[] DEFAULT '{}',
  visibility text DEFAULT 'private' CHECK (visibility IN ('private', 'public', 'shared')),
  memory_date date DEFAULT CURRENT_DATE,
  view_count integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_id text NOT NULL REFERENCES subscription_plans(id),
  stripe_subscription_id text UNIQUE,
  stripe_customer_id text,
  status text DEFAULT 'active' CHECK (status IN ('active', 'canceled', 'past_due', 'incomplete')),
  billing_cycle text DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'yearly')),
  current_period_start timestamptz,
  current_period_end timestamptz,
  cancel_at_period_end boolean DEFAULT false,
  stripe_price_id text,
  trial_end timestamptz,
  canceled_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create manual subscriptions table
CREATE TABLE IF NOT EXISTS manual_subscriptions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_id text DEFAULT 'unlimited',
  status text DEFAULT 'active' CHECK (status IN ('active', 'canceled')),
  billing_cycle text NOT NULL CHECK (billing_cycle IN ('monthly', 'yearly')),
  start_date date DEFAULT CURRENT_DATE,
  end_date date,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create other supporting tables
CREATE TABLE IF NOT EXISTS comments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  memory_id uuid NOT NULL,
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  comment_text text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS reactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  memory_id uuid NOT NULL,
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  emoji text NOT NULL CHECK (char_length(emoji) >= 1),
  created_at timestamptz DEFAULT now(),
  UNIQUE(memory_id, user_id, emoji)
);

CREATE TABLE IF NOT EXISTS shared_links (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  memory_id uuid NOT NULL,
  url_slug text UNIQUE NOT NULL,
  expires_at timestamptz,
  view_count integer DEFAULT 0,
  created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS tags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text UNIQUE NOT NULL,
  created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS memory_tags (
  memory_id uuid NOT NULL,
  tag_id uuid NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  PRIMARY KEY (memory_id, tag_id)
);

CREATE TABLE IF NOT EXISTS families (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  invite_code text UNIQUE NOT NULL,
  created_by uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS user_families (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  family_id uuid NOT NULL REFERENCES families(id) ON DELETE CASCADE,
  role text DEFAULT 'member' CHECK (role IN ('admin', 'member', 'viewer')),
  joined_at timestamptz DEFAULT now(),
  UNIQUE(user_id, family_id)
);

CREATE TABLE IF NOT EXISTS memory_collaborators (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  memory_id uuid NOT NULL,
  collaborator_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role text DEFAULT 'viewer' CHECK (role IN ('viewer', 'editor')),
  invited_at timestamptz DEFAULT now(),
  UNIQUE(memory_id, collaborator_id)
);

CREATE TABLE IF NOT EXISTS legacy_letters (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  recipient_name text NOT NULL,
  recipient_email text NOT NULL,
  delivery_date date NOT NULL,
  occasion text,
  subject text NOT NULL,
  message text NOT NULL,
  status text DEFAULT 'scheduled' CHECK (status IN ('draft', 'scheduled', 'delivered')),
  attached_memories uuid[] DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Insert default subscription plans
INSERT INTO subscription_plans (id, name, description, price_monthly, price_yearly, memory_limit, features) VALUES
('free', 'Free', 'Perfect for getting started', 0, 0, 50, ARRAY['50 memories', 'Basic sharing', 'Standard support']),
('unlimited', 'Unlimited', 'For memory collectors', 1000, 12000, NULL, ARRAY['Unlimited memories', 'Advanced sharing', 'Priority support', 'Early access to features'])
ON CONFLICT (id) DO NOTHING;

-- Enable Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE memories ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE manual_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE shared_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE families ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_families ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_collaborators ENABLE ROW LEVEL SECURITY;
ALTER TABLE legacy_letters ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies

-- User Profiles
CREATE POLICY "Users can view all profiles" ON user_profiles FOR SELECT TO authenticated USING (true);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT TO authenticated WITH CHECK (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE TO authenticated USING (auth.uid() = id);

-- Memories
CREATE POLICY "Users can view own memories" ON memories FOR SELECT TO authenticated USING (user_id = auth.uid());
CREATE POLICY "Users can view public memories" ON memories FOR SELECT TO authenticated USING (visibility = 'public');
CREATE POLICY "Users can insert own memories" ON memories FOR INSERT TO authenticated WITH CHECK (user_id = auth.uid());
CREATE POLICY "Users can update own memories" ON memories FOR UPDATE TO authenticated USING (user_id = auth.uid());
CREATE POLICY "Users can delete own memories" ON memories FOR DELETE TO authenticated USING (user_id = auth.uid());

-- Subscriptions
CREATE POLICY "Users can view own subscriptions" ON subscriptions FOR SELECT TO authenticated USING (user_id = auth.uid());
CREATE POLICY "Users can insert own subscriptions" ON subscriptions FOR INSERT TO authenticated WITH CHECK (user_id = auth.uid());
CREATE POLICY "Users can update own subscriptions" ON subscriptions FOR UPDATE TO authenticated USING (user_id = auth.uid());

-- Manual Subscriptions
CREATE POLICY "Users can view own manual subscriptions" ON manual_subscriptions FOR SELECT TO authenticated USING (user_id = auth.uid());

-- Comments
CREATE POLICY "Users can update own comments" ON comments FOR UPDATE TO authenticated USING (user_id = auth.uid());
CREATE POLICY "Users can delete own comments" ON comments FOR DELETE TO authenticated USING (user_id = auth.uid());

-- Reactions
CREATE POLICY "Users can manage own reactions" ON reactions FOR ALL TO authenticated USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());

-- Shared Links
CREATE POLICY "Anyone can view non-expired shared links" ON shared_links FOR SELECT TO anon, authenticated USING (expires_at IS NULL OR expires_at > now());

-- Tags
CREATE POLICY "Anyone can view tags" ON tags FOR SELECT TO authenticated USING (true);
CREATE POLICY "Users can create tags" ON tags FOR INSERT TO authenticated WITH CHECK (true);

-- Families
CREATE POLICY "Users can view families they belong to" ON families FOR SELECT TO authenticated USING (
  id IN (SELECT family_id FROM user_families WHERE user_id = auth.uid())
);
CREATE POLICY "Users can create families" ON families FOR INSERT TO authenticated WITH CHECK (created_by = auth.uid());

-- User Families
CREATE POLICY "Users can view their family memberships" ON user_families FOR SELECT TO authenticated USING (user_id = auth.uid());
CREATE POLICY "Users can join families" ON user_families FOR INSERT TO authenticated WITH CHECK (user_id = auth.uid());

-- Memory Collaborators
CREATE POLICY "Users can view own collaborations" ON memory_collaborators FOR SELECT TO authenticated USING (collaborator_id = auth.uid());

-- Legacy Letters
CREATE POLICY "Users can view their own letters" ON legacy_letters FOR SELECT TO authenticated USING (user_id = auth.uid());
CREATE POLICY "Users can insert their own letters" ON legacy_letters FOR INSERT TO authenticated WITH CHECK (user_id = auth.uid());
CREATE POLICY "Users can update their own letters" ON legacy_letters FOR UPDATE TO authenticated USING (user_id = auth.uid());
CREATE POLICY "Users can delete their own letters" ON legacy_letters FOR DELETE TO authenticated USING (user_id = auth.uid());

-- Subscription Plans
CREATE POLICY "Anyone can view subscription plans" ON subscription_plans FOR SELECT TO anon, authenticated USING (true);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_memories_user_id ON memories(user_id);
CREATE INDEX IF NOT EXISTS idx_memories_visibility ON memories(visibility);
CREATE INDEX IF NOT EXISTS idx_memories_created_at ON memories(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_subscription_id ON subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer_id ON subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_price_id ON subscriptions(stripe_price_id);
CREATE INDEX IF NOT EXISTS idx_manual_subscriptions_user_id ON manual_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_manual_subscriptions_status ON manual_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_comments_memory_id ON comments(memory_id);
CREATE INDEX IF NOT EXISTS idx_reactions_memory_id ON reactions(memory_id);
CREATE INDEX IF NOT EXISTS idx_shared_links_url_slug ON shared_links(url_slug);
CREATE INDEX IF NOT EXISTS idx_memory_tags_memory_id ON memory_tags(memory_id);
CREATE INDEX IF NOT EXISTS idx_memory_tags_tag_id ON memory_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_memory_collaborators_memory_id ON memory_collaborators(memory_id);
CREATE INDEX IF NOT EXISTS idx_memory_collaborators_collaborator_id ON memory_collaborators(collaborator_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON comments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO user_profiles (id, display_name)
  VALUES (NEW.id, COALESCE(NEW.raw_user_meta_data->>'display_name', split_part(NEW.email, '@', 1)));
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Create function to get subscription info
CREATE OR REPLACE FUNCTION get_subscription_info(user_uuid uuid)
RETURNS TABLE (
  plan_id text,
  plan_name text,
  memory_limit integer,
  memory_count bigint,
  can_upload boolean,
  status text,
  current_period_end timestamptz
) AS $$
BEGIN
  RETURN QUERY
  WITH user_memory_count AS (
    SELECT COUNT(*) as count FROM memories WHERE user_id = user_uuid
  ),
  user_subscription AS (
    SELECT s.plan_id, s.status, s.current_period_end, sp.name, sp.memory_limit
    FROM subscriptions s
    JOIN subscription_plans sp ON s.plan_id = sp.id
    WHERE s.user_id = user_uuid AND s.status = 'active'
    UNION ALL
    SELECT ms.plan_id, ms.status, ms.end_date, sp.name, sp.memory_limit
    FROM manual_subscriptions ms
    JOIN subscription_plans sp ON ms.plan_id = sp.id
    WHERE ms.user_id = user_uuid AND ms.status = 'active'
    LIMIT 1
  )
  SELECT 
    COALESCE(us.plan_id, 'free'),
    COALESCE(us.name, 'Free'),
    COALESCE(us.memory_limit, 50),
    umc.count,
    CASE 
      WHEN us.memory_limit IS NULL THEN true
      WHEN umc.count < us.memory_limit THEN true
      ELSE false
    END,
    COALESCE(us.status, 'active'),
    us.current_period_end
  FROM user_memory_count umc
  LEFT JOIN user_subscription us ON true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to increment view count
CREATE OR REPLACE FUNCTION increment_view_count(memory_uuid uuid)
RETURNS void AS $$
BEGIN
  UPDATE memories 
  SET view_count = view_count + 1 
  WHERE id = memory_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;