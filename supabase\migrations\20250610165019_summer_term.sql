/*
  # Complete Authentication System Fix
  
  This migration completely rebuilds the authentication system to ensure:
  1. Proper user profile creation
  2. Working RLS policies
  3. Functional triggers
  4. Clean database state
*/

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Clean up existing objects
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- Drop and recreate user_profiles table
DROP TABLE IF EXISTS user_profiles CASCADE;

CREATE TABLE user_profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  display_name text NOT NULL,
  avatar_url text,
  bio text DEFAULT '',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view all profiles" ON user_profiles 
  FOR SELECT TO authenticated USING (true);

CREATE POLICY "Users can insert own profile" ON user_profiles 
  FOR INSERT TO authenticated WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles 
  FOR UPDATE TO authenticated USING (auth.uid() = id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at trigger
CREATE TRIGGER update_user_profiles_updated_at 
  BEFORE UPDATE ON user_profiles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create robust user creation function
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  display_name_value text;
BEGIN
  -- Extract display name from metadata with multiple fallbacks
  display_name_value := COALESCE(
    NEW.raw_user_meta_data->>'display_name',
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'name',
    split_part(NEW.email, '@', 1),
    'User'
  );
  
  -- Insert user profile
  INSERT INTO user_profiles (id, display_name, bio)
  VALUES (
    NEW.id, 
    display_name_value,
    'Welcome to Nostoria! Start preserving your precious memories.'
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail user creation
    RAISE WARNING 'Failed to create user profile for %: %', NEW.email, SQLERRM;
    
    -- Try a minimal insert as fallback
    BEGIN
      INSERT INTO user_profiles (id, display_name) 
      VALUES (NEW.id, COALESCE(split_part(NEW.email, '@', 1), 'User'));
    EXCEPTION
      WHEN OTHERS THEN
        RAISE WARNING 'Fallback profile creation also failed for %', NEW.email;
    END;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new users
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Ensure subscription plans exist
INSERT INTO subscription_plans (id, name, description, price_monthly, price_yearly, memory_limit, features) VALUES
('free', 'Free', 'Perfect for getting started', 0, 0, 50, ARRAY['50 memories', 'Basic sharing', 'Standard support']),
('unlimited', 'Unlimited', 'For memory collectors', 1000, 12000, NULL, ARRAY['Unlimited memories', 'Advanced sharing', 'Priority support', 'Early access to features'])
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  price_monthly = EXCLUDED.price_monthly,
  price_yearly = EXCLUDED.price_yearly,
  memory_limit = EXCLUDED.memory_limit,
  features = EXCLUDED.features;

-- Create subscription info function with error handling
CREATE OR REPLACE FUNCTION get_subscription_info(user_uuid uuid)
RETURNS TABLE (
  plan_id text,
  plan_name text,
  memory_limit integer,
  memory_count bigint,
  can_upload boolean,
  status text,
  current_period_end timestamptz
) AS $$
BEGIN
  RETURN QUERY
  WITH user_memory_count AS (
    SELECT COUNT(*) as count FROM memories WHERE user_id = user_uuid
  ),
  user_subscription AS (
    SELECT s.plan_id, s.status, s.current_period_end, sp.name, sp.memory_limit
    FROM subscriptions s
    JOIN subscription_plans sp ON s.plan_id = sp.id
    WHERE s.user_id = user_uuid AND s.status = 'active'
    UNION ALL
    SELECT ms.plan_id, ms.status, ms.end_date, sp.name, sp.memory_limit
    FROM manual_subscriptions ms
    JOIN subscription_plans sp ON ms.plan_id = sp.id
    WHERE ms.user_id = user_uuid AND ms.status = 'active'
    LIMIT 1
  )
  SELECT 
    COALESCE(us.plan_id, 'free'),
    COALESCE(us.name, 'Free'),
    COALESCE(us.memory_limit, 50),
    COALESCE(umc.count, 0),
    CASE 
      WHEN us.memory_limit IS NULL THEN true
      WHEN COALESCE(umc.count, 0) < us.memory_limit THEN true
      ELSE false
    END,
    COALESCE(us.status, 'active'),
    us.current_period_end
  FROM user_memory_count umc
  LEFT JOIN user_subscription us ON true;
EXCEPTION
  WHEN OTHERS THEN
    -- Return safe defaults on any error
    RETURN QUERY SELECT 
      'free'::text,
      'Free'::text,
      50::integer,
      0::bigint,
      true::boolean,
      'active'::text,
      NULL::timestamptz;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated, anon;
GRANT ALL ON user_profiles TO authenticated;
GRANT SELECT ON subscription_plans TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_subscription_info(uuid) TO authenticated;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_display_name ON user_profiles(display_name);
CREATE INDEX IF NOT EXISTS idx_user_profiles_created_at ON user_profiles(created_at);

-- Test the setup
DO $$
BEGIN
  RAISE NOTICE 'Authentication system setup completed successfully at %', now();
END $$;