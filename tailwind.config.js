/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    extend: {
      fontFamily: {
        'serif': ['Crimson Text', 'serif'],
        'sans': ['Inter', 'sans-serif'],
      },
      colors: {
        // Your exact color palette
        ocean: '#053BA6',      // Deep blue
        sky: '#55B3D9',        // Light blue  
        teal: '#0396A6',       // Teal
        emerald: '#04BF68',    // Green
        coral: '#F28066',      // Coral/salmon
        // Extended palette for UI needs
        primary: {
          50: '#e6f2ff',
          100: '#cce5ff',
          200: '#99ccff',
          300: '#66b3ff',
          400: '#55B3D9',
          500: '#0396A6',
          600: '#053BA6',
          700: '#042f85',
          800: '#032464',
          900: '#021943',
        },
        secondary: {
          50: '#e6fdf9',
          100: '#ccfbf3',
          200: '#99f7e7',
          300: '#66f3db',
          400: '#33efcf',
          500: '#04BF68',
          600: '#0396A6',
          700: '#027885',
          800: '#015a64',
          900: '#003c43',
        },
        accent: {
          50: '#fff4f1',
          100: '#ffe9e3',
          200: '#ffd3c7',
          300: '#ffbdab',
          400: '#ffa78f',
          500: '#F28066',
          600: '#e56b4a',
          700: '#c8563e',
          800: '#ab4132',
          900: '#8e2c26',
        },
      },
      backgroundImage: {
        'ocean-gradient': 'linear-gradient(135deg, #053BA6 0%, #0396A6 50%, #04BF68 100%)',
        'sky-gradient': 'linear-gradient(135deg, #55B3D9 0%, #0396A6 100%)',
        'coral-gradient': 'linear-gradient(135deg, #F28066 0%, #55B3D9 100%)',
        'vibrant-mesh': 'radial-gradient(circle at 20% 50%, #053BA6 0%, transparent 50%), radial-gradient(circle at 80% 20%, #55B3D9 0%, transparent 50%), radial-gradient(circle at 40% 80%, #04BF68 0%, transparent 50%), radial-gradient(circle at 80% 80%, #F28066 0%, transparent 50%)',
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-gentle': 'bounce 2s infinite',
        'gradient-shift': 'gradient-shift 8s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
          '33%': { transform: 'translateY(-10px) rotate(2deg)' },
          '66%': { transform: 'translateY(-5px) rotate(-1deg)' },
        },
        'gradient-shift': {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
      },
      boxShadow: {
        'ocean': '0 10px 40px -10px rgba(5, 59, 166, 0.4)',
        'sky': '0 10px 40px -10px rgba(85, 179, 217, 0.4)',
        'coral': '0 10px 40px -10px rgba(242, 128, 102, 0.4)',
        'emerald': '0 10px 40px -10px rgba(4, 191, 104, 0.4)',
        'vibrant': '0 20px 60px -10px rgba(85, 179, 217, 0.3), 0 10px 30px -5px rgba(242, 128, 102, 0.2)',
      },
    },
  },
  plugins: [],
};